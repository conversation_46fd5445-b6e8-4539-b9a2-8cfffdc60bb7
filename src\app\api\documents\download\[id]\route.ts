import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { FileStorage } from '@/lib/storage';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const generatedDoc = await prisma.generatedDocument.findUnique({
      where: { id },
      include: {
        template: true,
      },
    });

    if (!generatedDoc) {
      return NextResponse.json(
        { error: 'Documento no encontrado' },
        { status: 404 }
      );
    }

    const fileBuffer = await FileStorage.getFile(generatedDoc.filePath);
    
    const mimeType = generatedDoc.format === 'pdf' 
      ? 'application/pdf'
      : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    const fileName = `${generatedDoc.name}.${generatedDoc.format}`;

    await prisma.generatedDocument.update({
      where: { id },
      data: {
        status: 'DOWNLOADED',
      },
    });

    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('Error downloading document:', error);
    return NextResponse.json(
      { error: 'Error al descargar el documento' },
      { status: 500 }
    );
  }
}
