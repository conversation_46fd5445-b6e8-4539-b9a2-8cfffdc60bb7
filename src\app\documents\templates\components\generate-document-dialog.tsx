'use client';

import { useState } from 'react';
import { useAction } from 'zsa-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, FileText, Download } from 'lucide-react';
import { toast } from 'sonner';
import { generateDocument } from '@/features/document-template/actions';
import type { DocumentTemplateWithRelations } from '@/features/document-template/types';

interface GenerateDocumentDialogProps {
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly template: DocumentTemplateWithRelations | null;
  readonly onSuccess?: () => void;
}

const MOCK_CASES = [
  { id: 'INS-2025-001', debtorName: '<PERSON> <PERSON> Pérez' },
  { id: 'CON-2025-002', debtorName: 'Carlos Rodríguez Silva' },
  { id: 'ACU-2025-003', debtorName: 'Ana Martínez López' },
  { id: 'INS-2025-004', debtorName: 'Luis Fernando Castro' },
];

export function GenerateDocumentDialog({
  open,
  onOpenChange,
  template,
  onSuccess,
}: GenerateDocumentDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    caseId: '',
    format: 'docx' as 'docx' | 'pdf',
  });
  
  const [placeholderData, setPlaceholderData] = useState<Record<string, string>>({});

  const { execute: executeGenerate, isPending } = useAction(generateDocument, {
    onSuccess: ({ data }) => {
      toast.success('Documento generado exitosamente');
      setFormData({ name: '', caseId: '', format: 'docx' });
      setPlaceholderData({});
      onSuccess?.();
      onOpenChange(false);
    },
    onError: ({ err }) => {
      toast.error(err.message);
    },
  });

  const selectedCase = MOCK_CASES.find(c => c.id === formData.caseId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!template) {
      toast.error('No hay plantilla seleccionada');
      return;
    }

    if (!formData.name) {
      toast.error('El nombre del documento es requerido');
      return;
    }

    const data: Record<string, any> = { ...placeholderData };
    
    if (selectedCase) {
      data.DEBTOR_NAME = selectedCase.debtorName;
      data.CASE_NUMBER = formData.caseId;
    }

    executeGenerate({
      templateId: template.id,
      caseId: formData.caseId || undefined,
      name: formData.name,
      format: formData.format,
      data,
    });
  };

  const handlePlaceholderChange = (fieldName: string, value: string) => {
    setPlaceholderData(prev => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generar Documento</DialogTitle>
          <DialogDescription>
            Complete los datos para generar el documento desde la plantilla "{template.name}"
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Información del Documento
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Nombre del Documento *</label>
                  <Input
                    placeholder="Ej: Auto de Admisión - INS-2025-001"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Formato</label>
                  <Select 
                    onValueChange={(value: 'docx' | 'pdf') => setFormData(prev => ({ ...prev, format: value }))} 
                    value={formData.format}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="docx">Word (.docx)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Caso Asociado (Opcional)</label>
                <Select 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, caseId: value }))} 
                  value={formData.caseId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar caso" />
                  </SelectTrigger>
                  <SelectContent>
                    {MOCK_CASES.map((case_) => (
                      <SelectItem key={case_.id} value={case_.id}>
                        {case_.id} - {case_.debtorName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {template.placeholders && template.placeholders.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Datos de la Plantilla</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {template.placeholders.map((placeholder) => (
                    <div key={placeholder.id} className="space-y-2">
                      <label className="text-sm font-medium">
                        {placeholder.description}
                        {placeholder.isRequired && ' *'}
                      </label>
                      {placeholder.dataType === 'text' && (
                        <Input
                          placeholder={placeholder.defaultValue || `Ingrese ${placeholder.description.toLowerCase()}`}
                          value={placeholderData[placeholder.fieldName] || ''}
                          onChange={(e) => handlePlaceholderChange(placeholder.fieldName, e.target.value)}
                        />
                      )}
                      {placeholder.dataType === 'date' && (
                        <Input
                          type="date"
                          value={placeholderData[placeholder.fieldName] || ''}
                          onChange={(e) => handlePlaceholderChange(placeholder.fieldName, e.target.value)}
                        />
                      )}
                      {placeholder.dataType === 'number' && (
                        <Input
                          type="number"
                          placeholder={placeholder.defaultValue || '0'}
                          value={placeholderData[placeholder.fieldName] || ''}
                          onChange={(e) => handlePlaceholderChange(placeholder.fieldName, e.target.value)}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isPending}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Download className="mr-2 h-4 w-4" />
              Generar Documento
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
