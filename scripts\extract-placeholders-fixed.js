#!/usr/bin/env node

/**
 * <PERSON>ript to extract placeholders from existing templates and update the database
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Extract placeholders from template content using {{placeholder}} syntax
 */
function extractPlaceholders(content) {
  const regex = /\{\{([^}]+)\}\}/g;
  const matches = [];
  let match;

  while ((match = regex.exec(content)) !== null) {
    matches.push(match[1].trim());
  }

  return [...new Set(matches)]; // Remove duplicates
}

/**
 * Convert camelCase or snake_case to readable label
 */
function formatFieldLabel(fieldName) {
  return fieldName
    .replace(/([A-Z])/g, ' $1') // Add space before capitals
    .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
    .replace(/\b\w/g, (l) => l.toUpperCase()) // Capitalize first letter of each word
    .trim();
}

/**
 * Infer data type based on field name patterns
 */
function inferDataType(fieldName) {
  const lowerKey = fieldName.toLowerCase();

  // Date patterns
  if (
    lowerKey.includes('fecha') ||
    lowerKey.includes('date') ||
    lowerKey.includes('dia') ||
    lowerKey.includes('mes') ||
    lowerKey.includes('año')
  ) {
    return 'date';
  }

  // Currency patterns
  if (
    lowerKey.includes('monto') ||
    lowerKey.includes('valor') ||
    lowerKey.includes('precio') ||
    lowerKey.includes('deuda') ||
    lowerKey.includes('capital') ||
    lowerKey.includes('pago')
  ) {
    return 'currency';
  }

  // Email patterns
  if (lowerKey.includes('email') || lowerKey.includes('correo')) {
    return 'email';
  }

  // Number patterns
  if (
    lowerKey.includes('numero') ||
    lowerKey.includes('cantidad') ||
    lowerKey.includes('cedula') ||
    lowerKey.includes('nit') ||
    lowerKey.includes('telefono')
  ) {
    return 'number';
  }

  // Boolean patterns
  if (
    lowerKey.includes('tiene') ||
    lowerKey.includes('es') ||
    lowerKey.includes('activo') ||
    lowerKey.includes('convocado')
  ) {
    return 'boolean';
  }

  // Default to text
  return 'text';
}

/**
 * Get default value for common fields
 */
function getDefaultValue(fieldName) {
  const lowerKey = fieldName.toLowerCase();

  if (lowerKey.includes('fechaactual') || lowerKey.includes('fechahoy')) {
    return 'today';
  }

  if (lowerKey.includes('año') && lowerKey.includes('actual')) {
    return 'currentYear';
  }

  return undefined;
}

/**
 * Generate basic placeholder definitions from extracted keys
 */
function generatePlaceholderDefinitions(keys) {
  return keys.map((key) => ({
    placeholder: `{{${key}}}`,
    fieldName: key,
    description: formatFieldLabel(key),
    dataType: inferDataType(key),
    isRequired: true,
    defaultValue: getDefaultValue(key),
  }));
}

async function extractPlaceholdersFromTemplates() {
  try {
    console.log('Starting placeholder extraction...');

    // Get all templates
    const templates = await prisma.template.findMany({
      include: {
        placeholders: true,
      },
    });

    console.log(`Found ${templates.length} templates to process`);

    let totalPlaceholdersExtracted = 0;

    for (const template of templates) {
      console.log(`\nProcessing template: ${template.name}`);

      // Extract placeholders from content
      const placeholderKeys = extractPlaceholders(template.content);
      console.log(
        `Found ${placeholderKeys.length} placeholders: ${placeholderKeys.join(', ')}`,
      );

      if (placeholderKeys.length === 0) {
        console.log('No placeholders found, skipping...');
        continue;
      }

      // Generate placeholder definitions
      const definitions = generatePlaceholderDefinitions(placeholderKeys);

      // Check which placeholders already exist
      const existingPlaceholders = template.placeholders.map(
        (p) => p.fieldName,
      );
      const newDefinitions = definitions.filter(
        (def) => !existingPlaceholders.includes(def.fieldName),
      );

      if (newDefinitions.length === 0) {
        console.log('All placeholders already exist, skipping...');
        continue;
      }

      console.log(`Adding ${newDefinitions.length} new placeholders`);

      // Create new placeholder records
      for (const definition of newDefinitions) {
        await prisma.templatePlaceholder.create({
          data: {
            templateId: template.id,
            placeholder: definition.placeholder,
            fieldName: definition.fieldName,
            description: definition.description,
            dataType: definition.dataType,
            isRequired: definition.isRequired,
            defaultValue: definition.defaultValue,
          },
        });
        console.log(
          `  ✅ Added: ${definition.placeholder} (${definition.dataType})`,
        );
        totalPlaceholdersExtracted++;
      }
    }

    console.log(`\n🎉 Extraction complete!`);
    console.log(`Total new placeholders added: ${totalPlaceholdersExtracted}`);
  } catch (error) {
    console.error('Error extracting placeholders:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
extractPlaceholdersFromTemplates();
