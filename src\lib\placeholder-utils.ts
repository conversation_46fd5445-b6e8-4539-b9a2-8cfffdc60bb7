/**
 * Utility functions for handling template placeholders
 */

export type PlaceholderValueType = string | number | boolean;

export interface PlaceholderDefinition {
  id?: string;
  placeholder: string; // "{{nombreDeudor}}"
  fieldName: string; // "nombreDeudor"
  description: string; // "Nombre completo del deudor"
  dataType:
    | 'text'
    | 'number'
    | 'date'
    | 'currency'
    | 'email'
    | 'select'
    | 'boolean';
  isRequired: boolean;
  defaultValue?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
}

export interface PlaceholderValue {
  fieldName: string;
  value: PlaceholderValueType;
}

/**
 * Extract placeholders from template content using {{placeholder}} syntax
 */
export function extractPlaceholders(content: string): string[] {
  const regex = /\{\{([^}]+)\}\}/g;
  const matches: string[] = [];
  let match;

  while ((match = regex.exec(content)) !== null) {
    matches.push(match[1].trim());
  }

  return [...new Set(matches)]; // Remove duplicates
}

/**
 * Generate basic placeholder definitions from extracted keys
 */
export function generatePlaceholderDefinitions(
  keys: string[],
): Omit<PlaceholderDefinition, 'id'>[] {
  return keys.map((key) => ({
    placeholder: `{{${key}}}`,
    fieldName: key,
    description: formatFieldLabel(key),
    dataType: inferDataType(key),
    isRequired: true,
    defaultValue: getDefaultValue(key),
  }));
}

/**
 * Convert camelCase or snake_case to readable label
 */
export function formatFieldLabel(fieldName: string): string {
  return fieldName
    .replace(/([A-Z])/g, ' $1') // Add space before capitals
    .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
    .replace(/\b\w/g, (l) => l.toUpperCase()) // Capitalize first letter of each word
    .trim();
}

/**
 * Infer data type based on field name patterns
 */
export function inferDataType(
  fieldName: string,
): PlaceholderDefinition['dataType'] {
  const lowerKey = fieldName.toLowerCase();

  // Date patterns
  if (
    lowerKey.includes('fecha') ||
    lowerKey.includes('date') ||
    lowerKey.includes('dia') ||
    lowerKey.includes('mes') ||
    lowerKey.includes('año')
  ) {
    return 'date';
  }

  // Currency patterns
  if (
    lowerKey.includes('monto') ||
    lowerKey.includes('valor') ||
    lowerKey.includes('precio') ||
    lowerKey.includes('deuda') ||
    lowerKey.includes('capital') ||
    lowerKey.includes('pago')
  ) {
    return 'currency';
  }

  // Email patterns
  if (lowerKey.includes('email') || lowerKey.includes('correo')) {
    return 'email';
  }

  // Number patterns
  if (
    lowerKey.includes('numero') ||
    lowerKey.includes('cantidad') ||
    lowerKey.includes('cedula') ||
    lowerKey.includes('nit') ||
    lowerKey.includes('telefono')
  ) {
    return 'number';
  }

  // Boolean patterns
  if (
    lowerKey.includes('tiene') ||
    lowerKey.includes('es') ||
    lowerKey.includes('activo') ||
    lowerKey.includes('convocado')
  ) {
    return 'boolean';
  }

  // Default to text
  return 'text';
}

/**
 * Get default value for common fields
 */
export function getDefaultValue(fieldName: string): string | undefined {
  const lowerKey = fieldName.toLowerCase();

  if (lowerKey.includes('fechaactual') || lowerKey.includes('fechahoy')) {
    return 'today';
  }

  if (lowerKey.includes('año') && lowerKey.includes('actual')) {
    return 'currentYear';
  }

  return undefined;
}

/**
 * Fill placeholders in template content with actual values
 */
export function fillPlaceholders(
  content: string,
  values: PlaceholderValue[],
): string {
  let filledContent = content;

  values.forEach(({ fieldName, value }) => {
    const placeholder = `{{${fieldName}}}`;
    const formattedValue = formatValue(value, fieldName);
    filledContent = filledContent.replace(
      new RegExp(placeholder, 'g'),
      formattedValue,
    );
  });

  return filledContent;
}

/**
 * Format value based on its type and field name
 */
export function formatValue(
  value: PlaceholderValueType,
  fieldName: string,
): string {
  if (value === null || value === undefined) return '';

  const lowerKey = fieldName.toLowerCase();

  // Handle special values
  if (value === 'today') {
    return formatTodayDate();
  }
  if (value === 'currentYear') {
    return new Date().getFullYear().toString();
  }

  // Date formatting
  if (isDateField(lowerKey)) {
    return formatDateValue(value);
  }

  // Currency formatting
  if (isCurrencyField(lowerKey)) {
    return formatCurrencyValue(value);
  }

  // Boolean formatting
  if (typeof value === 'boolean') {
    return value ? 'Sí' : 'No';
  }

  return value.toString();
}

function formatTodayDate(): string {
  return new Date().toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function isDateField(lowerKey: string): boolean {
  return lowerKey.includes('fecha') || lowerKey.includes('date');
}

function isCurrencyField(lowerKey: string): boolean {
  return (
    lowerKey.includes('monto') ||
    lowerKey.includes('valor') ||
    lowerKey.includes('precio') ||
    lowerKey.includes('deuda') ||
    lowerKey.includes('capital')
  );
}

function formatDateValue(value: PlaceholderValueType): string {
  if (typeof value === 'string') {
    const dateRegex = /^\d{4}-\d{2}-\d{2}/;
    if (dateRegex.exec(value)) {
      return new Date(value).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }
  }
  return value.toString();
}

function formatCurrencyValue(value: PlaceholderValueType): string {
  const numValue =
    typeof value === 'number' ? value : parseFloat(value.toString());
  if (!isNaN(numValue)) {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
    }).format(numValue);
  }
  return value.toString();
}

/**
 * Validate placeholder value based on its definition
 */
export function validatePlaceholderValue(
  value: PlaceholderValueType,
  definition: PlaceholderDefinition,
): { isValid: boolean; error?: string } {
  // Required field validation
  if (
    definition.isRequired &&
    (value === '' || value === null || value === undefined)
  ) {
    return {
      isValid: false,
      error: `${definition.description} es requerido`,
    };
  }

  if (value === '' || value === null || value === undefined) {
    return { isValid: true }; // Optional field, empty is ok
  }

  // Type-specific validation
  switch (definition.dataType) {
    case 'email': {
      return validateEmailValue(value);
    }
    case 'number':
    case 'currency': {
      return validateNumericValue(value, definition);
    }
    case 'text': {
      return validateTextValue(value, definition);
    }
    case 'select': {
      return validateSelectValue(value, definition);
    }
    default:
      return { isValid: true };
  }
}

function validateEmailValue(value: PlaceholderValueType): {
  isValid: boolean;
  error?: string;
} {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (typeof value === 'string' && !emailRegex.test(value)) {
    return {
      isValid: false,
      error: 'Debe ser un email válido',
    };
  }
  return { isValid: true };
}

function validateNumericValue(
  value: PlaceholderValueType,
  definition: PlaceholderDefinition,
): { isValid: boolean; error?: string } {
  const numValue =
    typeof value === 'number' ? value : parseFloat(value.toString());
  if (isNaN(numValue)) {
    return {
      isValid: false,
      error: 'Debe ser un número válido',
    };
  }
  if (
    definition.validation?.min !== undefined &&
    numValue < definition.validation.min
  ) {
    return {
      isValid: false,
      error: `Debe ser mayor o igual a ${definition.validation.min}`,
    };
  }
  if (
    definition.validation?.max !== undefined &&
    numValue > definition.validation.max
  ) {
    return {
      isValid: false,
      error: `Debe ser menor o igual a ${definition.validation.max}`,
    };
  }
  return { isValid: true };
}

function validateTextValue(
  value: PlaceholderValueType,
  definition: PlaceholderDefinition,
): { isValid: boolean; error?: string } {
  const strValue = value.toString();
  if (
    definition.validation?.min !== undefined &&
    strValue.length < definition.validation.min
  ) {
    return {
      isValid: false,
      error: `Debe tener al menos ${definition.validation.min} caracteres`,
    };
  }
  if (
    definition.validation?.max !== undefined &&
    strValue.length > definition.validation.max
  ) {
    return {
      isValid: false,
      error: `Debe tener máximo ${definition.validation.max} caracteres`,
    };
  }
  if (definition.validation?.pattern) {
    const regex = new RegExp(definition.validation.pattern);
    if (!regex.test(strValue)) {
      return {
        isValid: false,
        error: 'Formato inválido',
      };
    }
  }
  return { isValid: true };
}

function validateSelectValue(
  value: PlaceholderValueType,
  definition: PlaceholderDefinition,
): { isValid: boolean; error?: string } {
  if (
    definition.validation?.options &&
    !definition.validation.options.includes(value.toString())
  ) {
    return {
      isValid: false,
      error: 'Debe seleccionar una opción válida',
    };
  }
  return { isValid: true };
}

/**
 * Get all placeholder values with defaults applied
 */
export function getPlaceholderValues(
  definitions: PlaceholderDefinition[],
  userValues: Partial<Record<string, PlaceholderValueType>> = {},
): PlaceholderValue[] {
  return definitions.map((def) => ({
    fieldName: def.fieldName,
    value: userValues[def.fieldName] ?? def.defaultValue ?? '',
  }));
}
