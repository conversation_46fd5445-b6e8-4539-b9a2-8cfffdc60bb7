# Word Document Template System - Implementation Plan

## Overview

This document outlines the complete implementation plan for integrating Word document templates with placeholder filling capabilities into your Next.js application. The system allows users to upload Word documents as templates and generate new documents by filling placeholders with database data.

## Architecture Summary

### Technology Stack
- **docxtemplater**: For Word document manipulation and placeholder replacement
- **Next.js API Routes**: For server-side document processing
- **Prisma**: For database operations and template metadata storage
- **File Storage**: Local file system (can be extended to cloud storage)
- **React Components**: For UI management

### Database Schema Enhancement

The following models have been added to your Prisma schema:

```prisma
model DocumentTemplate {
  id              String                    @id @default(cuid())
  name            String
  description     String?
  category        String // Maps to formularios folder structure
  subcategory     String? // For nested folders
  fileName        String // Original Word document filename
  filePath        String // Path to stored Word document
  fileSize        Int
  mimeType        String @default("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
  isActive        Boolean @default(true)
  usage           Int     @default(0)
  createdDate     DateTime @default(now())
  updatedDate     DateTime @updatedAt
  createdBy       String
  placeholders    TemplatePlaceholder[]
  generatedDocs   GeneratedDocument[]
  versions        TemplateVersion[]
}

model TemplatePlaceholder {
  id              String           @id @default(cuid())
  templateId      String
  placeholder     String // e.g., "{{DEBTOR_NAME}}", "{{CASE_NUMBER}}"
  fieldName       String // e.g., "debtorName", "caseNumber"
  description     String // Human-readable description
  dataType        String @default("text") // text, number, date, boolean
  isRequired      Boolean @default(false)
  defaultValue    String?
  validationRule  String? // Optional validation regex or rule
  template        DocumentTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
}

model GeneratedDocument {
  id          String           @id @default(cuid())
  templateId  String
  caseId      String?
  name        String
  filePath    String // Path to generated Word document
  format      String @default("docx") // docx, pdf
  status      String @default("GENERATED") // GENERATED, DOWNLOADED, SENT
  createdDate DateTime @default(now())
  createdBy   String
  template    DocumentTemplate @relation(fields: [templateId], references: [id])
  case        Case?            @relation(fields: [caseId], references: [id])
}
```

## Implementation Components

### 1. Core Services

#### File Storage Service (`src/lib/storage.ts`)
- Handles file upload, storage, and retrieval
- Manages directory structure for templates and generated documents
- Provides file validation and security

#### Document Processor (`src/lib/document-processor.ts`)
- Uses docxtemplater for Word document manipulation
- Extracts placeholders from uploaded templates
- Generates documents with filled placeholders
- Handles document validation and preview

### 2. Feature Layer

#### Schemas (`src/features/document-template/schemas.ts`)
- Zod validation schemas for all template operations
- Input validation for uploads, generation, and updates

#### Types (`src/features/document-template/types.ts`)
- TypeScript type definitions
- Interfaces for template data structures

#### Actions (`src/features/document-template/actions.ts`)
- Server actions for template management
- CRUD operations with proper error handling
- Integration with Prisma and file storage

### 3. UI Components

#### Upload Template Dialog
- Drag-and-drop file upload
- Category and subcategory selection
- Automatic placeholder extraction
- Form validation and error handling

#### Generate Document Dialog
- Template selection
- Placeholder data input
- Case association
- Format selection (DOCX/PDF)

#### Template Management Page
- Template listing with search and filters
- Category-based organization
- Usage statistics
- Template actions (generate, edit, delete)

### 4. API Routes

#### Document Download (`/api/documents/download/[id]`)
- Secure file download
- Proper MIME type handling
- Download tracking

## Placeholder System

### Placeholder Format
Templates use double curly braces for placeholders: `{{PLACEHOLDER_NAME}}`

### Common Placeholders
- `{{DEBTOR_NAME}}` - Nombre del deudor
- `{{CASE_NUMBER}}` - Número del caso
- `{{CASE_DATE}}` - Fecha del caso
- `{{HEARING_DATE}}` - Fecha de audiencia
- `{{OPERATOR_NAME}}` - Nombre del operador
- `{{DEBT_AMOUNT}}` - Monto de la deuda
- `{{ATTORNEY_NAME}}` - Nombre del apoderado

### Data Type Support
- **Text**: String values
- **Number**: Numeric values with formatting
- **Date**: Date values with localization
- **Boolean**: Yes/No values in Spanish

## Integration with Existing System

### Category Mapping
Your formularios folder structure maps to categories:
- `1. FORMATO PARA AUDIENCIAS` → `audiencias`
- `2. TIPOS DE FRACASO` → `fracasos`
- `3. TIPO DE ACUERDO` → `acuerdos`
- And so on...

### Case Integration
Generated documents can be associated with existing cases in your system, automatically filling case-related placeholders.

## Bulk Import Process

A script (`src/scripts/import-templates.ts`) is provided to import existing Word documents from the formularios folder:

```bash
npx tsx src/scripts/import-templates.ts
```

This will:
1. Scan the formularios directory
2. Extract placeholders from each document
3. Create database records
4. Store files in the template storage system

## Next Steps

### 1. Database Migration
Run the Prisma migration to add the new models:
```bash
npx prisma migrate dev --name add_document_templates
```

### 2. Install Dependencies
```bash
npm install docxtemplater pizzip jszip-sync
npm install --save-dev @types/pizzip
```

### 3. Environment Setup
Add to your `.env` file:
```
STORAGE_PATH=./storage
```

### 4. Import Existing Templates
```bash
npx tsx src/scripts/import-templates.ts
```

### 5. Add Navigation
Add a link to `/documents/templates` in your navigation menu.

## Advanced Features (Future Enhancements)

### 1. PDF Generation
- Convert generated DOCX to PDF using libraries like `puppeteer` or `libreoffice`
- Direct PDF template support

### 2. Template Versioning
- Track template changes over time
- Rollback capabilities
- Change logs

### 3. Conditional Logic
- Advanced placeholder logic with conditions
- Loop support for tables and lists
- Mathematical calculations

### 4. Cloud Storage Integration
- AWS S3, Google Cloud Storage, or Azure Blob Storage
- CDN integration for faster downloads
- Backup and redundancy

### 5. Template Editor
- In-browser Word document editing
- Visual placeholder management
- Real-time preview

### 6. Workflow Integration
- Automatic document generation based on case events
- Email integration for document distribution
- Digital signatures

## Security Considerations

1. **File Validation**: Only allow .docx files
2. **Size Limits**: Implement file size restrictions
3. **Access Control**: Ensure proper user permissions
4. **Path Traversal**: Validate file paths to prevent directory traversal
5. **Virus Scanning**: Consider integrating virus scanning for uploaded files

## Performance Optimization

1. **Caching**: Cache frequently used templates
2. **Async Processing**: Use background jobs for large document generation
3. **Compression**: Compress stored files
4. **CDN**: Use CDN for file delivery

This implementation provides a robust foundation for Word document template management with room for future enhancements based on your specific needs.
