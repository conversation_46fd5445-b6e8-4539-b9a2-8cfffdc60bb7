'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  FileText,
  Eye,
  Edit2,
  Download,
  Calendar,
  Hash,
  Code,
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Template {
  id: string;
  name: string;
  description: string | null;
  content: string;
  category: string;
  subcategory: string | null;
  usage: number;
  updatedDate: string;
  placeholders?: Array<{
    id: string;
    placeholder: string;
    fieldName: string;
    description: string;
    dataType: string;
    isRequired: boolean;
  }>;
}

interface TemplatePreviewDialogProps {
  readonly templateId: string | null;
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onEdit?: (templateId: string) => void;
  readonly onDownload?: (template: Template) => void;
}

export function TemplatePreviewDialog({
  templateId,
  open,
  onOpenChange,
  onEdit,
  onDownload,
}: TemplatePreviewDialogProps) {
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRawMarkdown, setShowRawMarkdown] = useState(false);

  const fetchTemplate = useCallback(async () => {
    if (!templateId) return;

    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/templates/${templateId}`);
      const data = await response.json();

      if (response.ok) {
        setTemplate(data);
      } else {
        setError(data.error || 'Error al cargar la plantilla');
      }
    } catch (err) {
      console.error('Error fetching template:', err);
      setError('Error al cargar la plantilla');
    } finally {
      setLoading(false);
    }
  }, [templateId]);

  useEffect(() => {
    if (templateId && open) {
      fetchTemplate();
    }
  }, [templateId, open, fetchTemplate]);

  const handleDownload = () => {
    if (!template) return;

    // Create blob with template content
    const blob = new Blob([template.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    // Create download link
    const link = document.createElement('a');
    link.href = url;
    link.download = `${template.name}.md`;
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // Call callback if provided
    onDownload?.(template);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      Inicial: 'bg-blue-100 text-blue-800',
      Admisión: 'bg-green-100 text-green-800',
      Notificación: 'bg-yellow-100 text-yellow-800',
      Suspensión: 'bg-orange-100 text-orange-800',
      Calificación: 'bg-purple-100 text-purple-800',
      Acuerdos: 'bg-emerald-100 text-emerald-800',
      Incumplimientos: 'bg-red-100 text-red-800',
      Reformas: 'bg-indigo-100 text-indigo-800',
      Rechazos: 'bg-gray-100 text-gray-800',
      Desistimientos: 'bg-pink-100 text-pink-800',
      Finalización: 'bg-slate-100 text-slate-800',
      Audiencias: 'bg-cyan-100 text-cyan-800',
      Fracasos: 'bg-rose-100 text-rose-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] w-[98vw] max-w-7xl">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-blue-600" />
              <div>
                <DialogTitle className="text-xl">
                  {loading
                    ? 'Cargando...'
                    : template?.name || 'Vista Previa de Plantilla'}
                </DialogTitle>
                <DialogDescription>
                  {template?.description ||
                    'Vista previa del contenido de la plantilla'}
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {template && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit?.(template.id)}
                  >
                    <Edit2 className="mr-2 h-4 w-4" />
                    Editar
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleDownload}>
                    <Download className="mr-2 h-4 w-4" />
                    Descargar
                  </Button>
                </>
              )}
            </div>
          </div>
        </DialogHeader>

        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
              <p className="text-muted-foreground mt-2">
                Cargando plantilla...
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="py-8 text-center">
            <p className="text-red-600">{error}</p>
            <Button onClick={fetchTemplate} className="mt-4" variant="outline">
              Reintentar
            </Button>
          </div>
        )}

        {template && (
          <Tabs defaultValue="preview" className="flex-1">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="preview">Vista Previa</TabsTrigger>
              <TabsTrigger value="placeholders">Marcadores</TabsTrigger>
              <TabsTrigger value="info">Información</TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="flex-1">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-medium">Contenido del Documento</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowRawMarkdown(!showRawMarkdown)}
                >
                  <Code className="mr-2 h-4 w-4" />
                  {showRawMarkdown ? 'Vista Formateada' : 'Ver Markdown'}
                </Button>
              </div>
              <ScrollArea className="h-[650px] w-full rounded-md border p-6">
                {showRawMarkdown ? (
                  <pre className="font-mono text-sm leading-relaxed whitespace-pre-wrap text-gray-700">
                    {template.content}
                  </pre>
                ) : (
                  <div className="prose prose-sm prose-headings:text-gray-900 prose-p:text-gray-700 prose-strong:text-gray-900 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:text-gray-700 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded max-w-none">
                    <ReactMarkdown>{template.content}</ReactMarkdown>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="placeholders" className="flex-1">
              <ScrollArea className="h-[600px] w-full">
                {template.placeholders && template.placeholders.length > 0 ? (
                  <div className="space-y-3">
                    {template.placeholders.map((placeholder) => (
                      <div
                        key={placeholder.id}
                        className="flex items-center justify-between rounded-lg border bg-gray-50 p-3"
                      >
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="font-mono">
                            {placeholder.placeholder}
                          </Badge>
                          <div>
                            <p className="font-medium">
                              {placeholder.description}
                            </p>
                            <p className="text-muted-foreground text-sm">
                              Tipo: {placeholder.dataType} •
                              {placeholder.isRequired
                                ? ' Requerido'
                                : ' Opcional'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <Hash className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-4 text-lg font-medium">Sin marcadores</h3>
                    <p className="text-gray-500">
                      Esta plantilla no tiene marcadores dinámicos
                    </p>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="info" className="flex-1">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <span className="text-muted-foreground text-sm font-medium">
                        Categoría
                      </span>
                      <div className="mt-1">
                        <Badge className={getCategoryColor(template.category)}>
                          {template.category}
                        </Badge>
                        {template.subcategory && (
                          <Badge variant="outline" className="ml-2">
                            {template.subcategory}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div>
                      <span className="text-muted-foreground text-sm font-medium">
                        Uso
                      </span>
                      <div className="mt-1 flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">
                          {template.usage} veces usado
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <span className="text-muted-foreground text-sm font-medium">
                        Última actualización
                      </span>
                      <div className="mt-1 flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="text-sm">
                          {formatDate(template.updatedDate)}
                        </span>
                      </div>
                    </div>

                    <div>
                      <span className="text-muted-foreground text-sm font-medium">
                        Marcadores
                      </span>
                      <div className="mt-1 flex items-center gap-2">
                        <Hash className="h-4 w-4" />
                        <span className="text-sm">
                          {template.placeholders
                            ? template.placeholders.length
                            : 0}{' '}
                          marcadores
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <span className="text-muted-foreground text-sm font-medium">
                    Descripción
                  </span>
                  <p className="mt-1 text-sm">
                    {template.description || 'Sin descripción disponible'}
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
}
