import { promises as fs } from 'fs';
import path from 'path';
import prisma from '@/lib/prisma';
import { FileStorage, getCategoryFromPath } from '@/lib/storage';
import { DocumentProcessor } from '@/lib/document-processor';

interface TemplateFile {
  name: string;
  path: string;
  category: string;
  subcategory?: string;
  size: number;
}

async function scanFormulariosDirectory(dirPath: string): Promise<TemplateFile[]> {
  const templates: TemplateFile[] = [];
  
  async function scanDirectory(currentPath: string, relativePath: string = '') {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);
      const relativeEntryPath = path.join(relativePath, entry.name);
      
      if (entry.isDirectory()) {
        await scanDirectory(fullPath, relativeEntryPath);
      } else if (entry.isFile() && entry.name.endsWith('.docx') && !entry.name.startsWith('~$')) {
        const stats = await fs.stat(fullPath);
        const { category, subcategory } = getCategoryFromPath(relativePath);
        
        templates.push({
          name: entry.name.replace('.docx', ''),
          path: fullPath,
          category: category || 'general',
          subcategory,
          size: stats.size,
        });
      }
    }
  }
  
  await scanDirectory(dirPath);
  return templates;
}

function mapCategoryName(folderName: string): string {
  const categoryMap: Record<string, string> = {
    '1. FORMATO PARA AUDIENCIAS': 'audiencias',
    '2. TIPOS DE FRACASO': 'fracasos',
    '3. TIPO DE ACUERDO': 'acuerdos',
    '4. SOLICITUD DE INCUMPLIMIENTO DEL ACUERDO': 'incumplimiento',
    '5. SOLICITUD DE REFORMA DEL ACUERDO': 'reforma',
    '6. TIPOS DE RECHAZO': 'rechazo',
    '7. DESISTIMIENTO': 'desistimiento',
  };
  
  return categoryMap[folderName] || folderName.toLowerCase().replace(/[^a-z0-9]/g, '_');
}

function generateTemplateName(fileName: string, category: string, subcategory?: string): string {
  let name = fileName
    .replace(/^\d+\.?\s*/, '') // Remove leading numbers
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase()); // Title case
  
  if (subcategory) {
    name = `${name} - ${subcategory}`;
  }
  
  return name;
}

async function importTemplate(templateFile: TemplateFile): Promise<void> {
  try {
    console.log(`Importing: ${templateFile.name}`);
    
    const fileBuffer = await fs.readFile(templateFile.path);
    const file = new File([fileBuffer], path.basename(templateFile.path), {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    
    const category = mapCategoryName(templateFile.category);
    const filePath = await FileStorage.saveTemplate(file, category);
    
    let placeholders;
    try {
      placeholders = await DocumentProcessor.extractPlaceholders(filePath);
    } catch (error) {
      console.warn(`Could not extract placeholders from ${templateFile.name}:`, error);
      placeholders = [];
    }
    
    const templateName = generateTemplateName(
      templateFile.name, 
      templateFile.category, 
      templateFile.subcategory
    );
    
    await prisma.documentTemplate.create({
      data: {
        name: templateName,
        description: `Plantilla importada desde ${templateFile.category}${templateFile.subcategory ? ` - ${templateFile.subcategory}` : ''}`,
        category,
        subcategory: templateFile.subcategory,
        fileName: path.basename(templateFile.path),
        filePath,
        fileSize: templateFile.size,
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        createdBy: 'system-import',
        placeholders: {
          create: placeholders.map(p => ({
            placeholder: p.placeholder,
            fieldName: p.fieldName,
            description: p.description,
            dataType: p.dataType,
            isRequired: p.isRequired,
            defaultValue: p.defaultValue,
          })),
        },
        versions: {
          create: {
            version: '1.0',
            filePath,
            changelog: 'Importación inicial desde formularios',
          },
        },
      },
    });
    
    console.log(`✅ Imported: ${templateName}`);
  } catch (error) {
    console.error(`❌ Failed to import ${templateFile.name}:`, error);
  }
}

async function main() {
  try {
    console.log('🚀 Starting template import process...');
    
    const formulariosPath = path.join(process.cwd(), 'formularios');
    
    if (!(await fs.access(formulariosPath).then(() => true).catch(() => false))) {
      console.error('❌ Formularios directory not found at:', formulariosPath);
      process.exit(1);
    }
    
    console.log('📁 Scanning formularios directory...');
    const templates = await scanFormulariosDirectory(formulariosPath);
    
    console.log(`📋 Found ${templates.length} Word documents to import`);
    
    for (const template of templates) {
      await importTemplate(template);
    }
    
    console.log('✅ Template import completed successfully!');
    
    const stats = await prisma.documentTemplate.groupBy({
      by: ['category'],
      _count: {
        category: true,
      },
    });
    
    console.log('\n📊 Import Summary:');
    stats.forEach(stat => {
      console.log(`  ${stat.category}: ${stat._count.category} templates`);
    });
    
  } catch (error) {
    console.error('❌ Import process failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { main as importTemplates };
