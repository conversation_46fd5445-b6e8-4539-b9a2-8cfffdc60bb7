import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, caseId, data, name, format = 'markdown' } = body;

    // Get the template
    const template = await prisma.template.findUnique({
      where: { id: templateId },
      include: { placeholders: true },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 },
      );
    }

    // Replace placeholders in template content
    let generatedContent = template.content;

    template.placeholders.forEach((placeholder) => {
      const value =
        data[placeholder.fieldName] || placeholder.defaultValue || '';
      const regex = new RegExp(
        placeholder.placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        'g',
      );
      generatedContent = generatedContent.replace(regex, value);
    });

    // Create generated document record
    const generatedDocument = await prisma.generatedDocument.create({
      data: {
        templateId,
        caseId,
        name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
        content: generatedContent,
        format,
      },
    });

    // Update template usage count
    await prisma.template.update({
      where: { id: templateId },
      data: {
        usage: {
          increment: 1,
        },
      },
    });

    return NextResponse.json(generatedDocument);
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 },
    );
  }
}
