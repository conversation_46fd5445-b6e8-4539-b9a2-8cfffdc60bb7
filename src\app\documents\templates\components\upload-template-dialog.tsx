'use client';

import { useState } from 'react';
import { useAction } from 'zsa-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, FileText, X, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { uploadTemplate } from '@/features/document-template/actions';

interface UploadTemplateDialogProps {
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onSuccess?: () => void;
}

const TEMPLATE_CATEGORIES = [
  { value: 'admision', label: 'Admisión' },
  { value: 'audiencias', label: 'Audiencias' },
  { value: 'notificacion', label: 'Notificación' },
  { value: 'suspension', label: 'Suspensión' },
  { value: 'acuerdos', label: 'Acuerdos' },
  { value: 'fracasos', label: 'Fracasos' },
  { value: 'finalizacion', label: 'Finalización' },
  { value: 'otros', label: 'Otros' },
];

const SUBCATEGORIES: Record<string, string[]> = {
  fracasos: [
    'Acuerdo Bilateral',
    'Falta de Pago Gastos',
    'Impugnación del Acuerdo',
    'Inasistencia y Vencimiento',
    'Inasistencia - Ánimo de Conciliar',
    'Incumplimiento',
    'Muerte del Deudor',
    'Propuesta Mala',
    'Propuesta Mala y Quorum',
    'Propuesta Mala y Vencimiento',
    'Reforma del Acuerdo',
    'Vencimiento de Términos',
  ],
  acuerdos: ['Acuerdo Bilateral', 'Acuerdo de Pago', 'Reforma Acuerdo'],
  audiencias: [
    'Primera Audiencia',
    'Audiencia de Seguimiento',
    'Audiencia Final',
  ],
};

export function UploadTemplateDialog({
  open,
  onOpenChange,
  onSuccess,
}: UploadTemplateDialogProps) {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    subcategory: '',
  });

  const { execute: executeUpload, isPending } = useAction(uploadTemplate, {
    onSuccess: ({ data }) => {
      toast.success('Plantilla subida exitosamente');
      setFormData({ name: '', description: '', category: '', subcategory: '' });
      setSelectedFile(null);
      onSuccess?.();
      onOpenChange(false);
    },
    onError: ({ err }) => {
      toast.error(err.message);
    },
  });

  const availableSubcategories = SUBCATEGORIES[formData.category] || [];

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const wordFile = files.find(
      (file) =>
        file.type ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.name.endsWith('.docx'),
    );

    if (wordFile) {
      setSelectedFile(wordFile);
      form.setValue('file', wordFile);
      if (!form.getValues('name')) {
        form.setValue('name', wordFile.name.replace('.docx', ''));
      }
    } else {
      toast.error('Solo se permiten archivos de Word (.docx)');
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      if (!formData.name) {
        setFormData((prev) => ({
          ...prev,
          name: file.name.replace('.docx', ''),
        }));
      }
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      toast.error('Debe seleccionar un archivo');
      return;
    }

    if (!formData.name || !formData.category) {
      toast.error('Complete todos los campos obligatorios');
      return;
    }

    executeUpload({
      ...formData,
      file: selectedFile,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Subir Plantilla de Documento</DialogTitle>
          <DialogDescription>
            Suba una plantilla de Word con marcadores de posición para generar
            documentos automáticamente
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <div
                className={`rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                  dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {selectedFile ? (
                  <div className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-8 w-8 text-blue-500" />
                      <div className="text-left">
                        <p className="font-medium">{selectedFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <>
                    <Upload className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                    <p className="mb-2 text-lg font-medium">
                      Arrastra un archivo de Word aquí o haz clic para
                      seleccionar
                    </p>
                    <p className="mb-4 text-sm text-gray-600">
                      Solo archivos .docx (Máximo 10MB)
                    </p>
                    <input
                      type="file"
                      accept=".docx"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="file-upload"
                    />
                    <Button asChild variant="outline">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        Seleccionar Archivo
                      </label>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Nombre de la Plantilla *
              </label>
              <Input
                placeholder="Ej: Auto de Admisión"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Categoría *</label>
              <Select
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    category: value,
                    subcategory: '',
                  }))
                }
                value={formData.category}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {TEMPLATE_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {availableSubcategories.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Subcategoría</label>
              <Select
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, subcategory: value }))
                }
                value={formData.subcategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar subcategoría" />
                </SelectTrigger>
                <SelectContent>
                  {availableSubcategories.map((subcategory) => (
                    <SelectItem key={subcategory} value={subcategory}>
                      {subcategory}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium">Descripción</label>
            <Textarea
              placeholder="Descripción opcional de la plantilla..."
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isPending}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Subir Plantilla
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
