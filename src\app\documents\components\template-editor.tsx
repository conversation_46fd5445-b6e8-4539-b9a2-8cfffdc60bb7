'use client';

import { useState, useEffect, useCallback } from 'react';
import { Save, X, Eye, Plus, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface TemplatePlaceholder {
  id?: string;
  placeholder: string;
  fieldName: string;
  description: string;
  dataType: string;
  isRequired: boolean;
  defaultValue?: string;
}

interface Template {
  id?: string;
  name: string;
  description: string;
  content: string;
  category: string;
  subcategory?: string;
  placeholders: TemplatePlaceholder[];
}

interface TemplateEditorProps {
  readonly templateId?: string;
  readonly onSave?: (template: Template) => void;
  readonly onCancel?: () => void;
}

export function TemplateEditor({
  templateId,
  onSave,
  onCancel,
}: TemplateEditorProps) {
  const [template, setTemplate] = useState<Template>({
    name: '',
    description: '',
    content: '',
    category: '',
    subcategory: '',
    placeholders: [],
  });

  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [newPlaceholder, setNewPlaceholder] = useState<TemplatePlaceholder>({
    placeholder: '',
    fieldName: '',
    description: '',
    dataType: 'text',
    isRequired: true,
  });

  const fetchTemplate = useCallback(async () => {
    if (!templateId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/templates/${templateId}`);
      const data = await response.json();

      if (response.ok) {
        setTemplate(data);
      }
    } catch (error) {
      console.error('Error fetching template:', error);
    } finally {
      setLoading(false);
    }
  }, [templateId]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/templates/categories');
      const data = await response.json();

      if (response.ok) {
        setCategories(data.map((cat: { name: string }) => cat.name));
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  useEffect(() => {
    const loadData = async () => {
      await fetchCategories();
      if (templateId) {
        await fetchTemplate();
      }
    };
    loadData();
  }, [templateId, fetchCategories, fetchTemplate]);

  const handleSave = async () => {
    try {
      setSaving(true);

      const url = templateId
        ? `/api/templates/${templateId}`
        : '/api/templates';
      const method = templateId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...template,
          createVersion: !!templateId, // Create version for updates
          changelog: templateId
            ? 'Template updated via editor'
            : 'Template created',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        onSave?.(data);
      } else {
        console.error('Error saving template:', data.error);
      }
    } catch (error) {
      console.error('Error saving template:', error);
    } finally {
      setSaving(false);
    }
  };

  const addPlaceholder = () => {
    if (newPlaceholder.fieldName && newPlaceholder.placeholder) {
      setTemplate((prev) => ({
        ...prev,
        placeholders: [...prev.placeholders, { ...newPlaceholder }],
      }));

      setNewPlaceholder({
        placeholder: '',
        fieldName: '',
        description: '',
        dataType: 'text',
        isRequired: true,
      });
    }
  };

  const removePlaceholder = (index: number) => {
    setTemplate((prev) => ({
      ...prev,
      placeholders: prev.placeholders.filter((_, i) => i !== index),
    }));
  };

  const detectPlaceholders = () => {
    const placeholderRegex = /\{\{([^}]+)\}\}/g;
    const found: Set<string> = new Set();
    let match;

    while ((match = placeholderRegex.exec(template.content)) !== null) {
      found.add(match[1]);
    }

    const newPlaceholders: TemplatePlaceholder[] = Array.from(found)
      .filter(
        (fieldName) =>
          !template.placeholders.some((p) => p.fieldName === fieldName),
      )
      .map((fieldName) => ({
        placeholder: `{{${fieldName}}}`,
        fieldName,
        description: `Campo ${fieldName}`,
        dataType: 'text',
        isRequired: true,
      }));

    if (newPlaceholders.length > 0) {
      setTemplate((prev) => ({
        ...prev,
        placeholders: [...prev.placeholders, ...newPlaceholders],
      }));
    }
  };

  if (loading) {
    return <div className="p-8">Cargando plantilla...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {templateId ? 'Editar Plantilla' : 'Nueva Plantilla'}
          </h2>
          <p className="text-muted-foreground">
            {templateId
              ? 'Modifica la plantilla existente'
              : 'Crea una nueva plantilla de documento'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {previewMode ? 'Editar' : 'Vista Previa'}
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Guardando...' : 'Guardar'}
          </Button>
          <Button variant="outline" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancelar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="content" className="space-y-4">
        <TabsList>
          <TabsTrigger value="content">Contenido</TabsTrigger>
          <TabsTrigger value="placeholders">Marcadores</TabsTrigger>
          <TabsTrigger value="settings">Configuración</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contenido de la Plantilla</CardTitle>
              <CardDescription>
                Escribe el contenido usando marcadores como {`{{nombre_campo}}`}{' '}
                para campos dinámicos
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewMode ? (
                <div className="prose max-w-none rounded-lg border bg-gray-50 p-4">
                  <pre className="font-sans whitespace-pre-wrap">
                    {template.content}
                  </pre>
                </div>
              ) : (
                <div className="space-y-4">
                  <Textarea
                    value={template.content}
                    onChange={(e) =>
                      setTemplate((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    placeholder="Ingresa el contenido de la plantilla..."
                    className="min-h-[400px] font-mono"
                  />
                  <Button
                    onClick={detectPlaceholders}
                    variant="outline"
                    size="sm"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Detectar Marcadores
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="placeholders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Marcadores de Posición</CardTitle>
              <CardDescription>
                Define los campos dinámicos que se reemplazarán al generar
                documentos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add new placeholder */}
              <div className="grid grid-cols-5 gap-2 rounded-lg border p-4">
                <Input
                  placeholder="{{campo}}"
                  value={newPlaceholder.placeholder}
                  onChange={(e) =>
                    setNewPlaceholder((prev) => ({
                      ...prev,
                      placeholder: e.target.value,
                    }))
                  }
                />
                <Input
                  placeholder="nombre_campo"
                  value={newPlaceholder.fieldName}
                  onChange={(e) =>
                    setNewPlaceholder((prev) => ({
                      ...prev,
                      fieldName: e.target.value,
                    }))
                  }
                />
                <Input
                  placeholder="Descripción"
                  value={newPlaceholder.description}
                  onChange={(e) =>
                    setNewPlaceholder((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                />
                <Select
                  value={newPlaceholder.dataType}
                  onValueChange={(value) =>
                    setNewPlaceholder((prev) => ({ ...prev, dataType: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Texto</SelectItem>
                    <SelectItem value="date">Fecha</SelectItem>
                    <SelectItem value="number">Número</SelectItem>
                    <SelectItem value="currency">Moneda</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={addPlaceholder} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* Existing placeholders */}
              <div className="space-y-2">
                {template.placeholders.map((placeholder, index) => (
                  <div
                    key={`placeholder-${placeholder.fieldName}-${index}`}
                    className="flex items-center gap-2 rounded border p-2"
                  >
                    <Badge variant="outline">{placeholder.placeholder}</Badge>
                    <span className="flex-1">{placeholder.description}</span>
                    <Badge variant="secondary">{placeholder.dataType}</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removePlaceholder(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuración de la Plantilla</CardTitle>
              <CardDescription>
                Información básica y categorización de la plantilla
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre</Label>
                  <Input
                    id="name"
                    value={template.name}
                    onChange={(e) =>
                      setTemplate((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="Nombre de la plantilla"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Categoría</Label>
                  <Select
                    value={template.category}
                    onValueChange={(value) =>
                      setTemplate((prev) => ({ ...prev, category: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona una categoría" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subcategory">Subcategoría (opcional)</Label>
                <Input
                  id="subcategory"
                  value={template.subcategory || ''}
                  onChange={(e) =>
                    setTemplate((prev) => ({
                      ...prev,
                      subcategory: e.target.value,
                    }))
                  }
                  placeholder="Subcategoría"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descripción</Label>
                <Textarea
                  id="description"
                  value={template.description}
                  onChange={(e) =>
                    setTemplate((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Descripción de la plantilla"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
