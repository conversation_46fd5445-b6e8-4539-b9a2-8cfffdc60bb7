import { z } from 'zod';

const mapDocumentTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    CEDULA: 'Cédula',
    RUT: 'RUT',
    ESTADOS_FINANCIEROS: 'Estados Financieros',
    CERTIFICADO_INGRESOS: 'Certificado de Ingresos',
    ESCRITURA_PUBLICA: 'Escritura Pública',
    AUTORIZACION: 'Autorización',
    DEMANDA: 'Demanda',
    RESPUESTA_DEMANDA: 'Respuesta a Demanda',
    ACUERDO: 'Acuerdo',
    SENTENCIA: 'Sentencia',
    OTRO: 'Otro',
  };
  return typeMap[type] || type;
});

const mapDocumentStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    PENDIENTE: 'Pendiente',
    APROBADO: 'Aprobado',
    RECHAZADO: 'Rechazado',
    EN_REVISION: 'En Revisión',
  };
  return statusMap[status] || status;
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
});

const caseForDocumentSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
});

export const documentWithCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: mapDocumentTypeToSpanish,
  status: mapDocumentStatusToSpanish,
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
  case: caseForDocumentSchema,
});

export type DocumentWithCase = z.infer<typeof documentWithCaseSchema>;

export const documentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string().optional(),
  uploadDate: z.coerce.date().optional(),
  caseId: z.string().optional(),
  case: caseForDocumentSchema.optional(),
  debtorName: z.string().optional(),
  category: z.string().optional(),
  createdDate: z.string().optional(),
  size: z.string().optional(),
  format: z.string().optional(),
  createdBy: z.string().optional(),
  downloadCount: z.coerce.number().optional(),
  lastAccessed: z.string().optional(),
  content: z.string().optional(),
  viewCount: z.coerce.number().optional(),
  shareCount: z.coerce.number().optional(),
});

export type Document = z.infer<typeof documentSchema>;

export const documentFilterSchema = z.object({
  caseId: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
});

export type DocumentFilter = z.infer<typeof documentFilterSchema>;

export const documentStatsSchema = z.object({
  total: z.coerce.number(),
  byStatus: z.array(
    z.object({
      status: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
  byType: z.array(
    z.object({
      type: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
});

export type DocumentStats = z.infer<typeof documentStatsSchema>;

export const createDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  type: z.string().min(1, 'El tipo es requerido'),
  status: z.string().optional().default('PENDIENTE'),
  url: z.string().url('URL inválida'),
  caseId: z.string().min(1, 'El ID del caso es requerido'),
});

export type CreateDocumentData = z.infer<typeof createDocumentSchema>;

export const updateDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  status: z.string().optional(),
  url: z.string().url('URL inválida').optional(),
  caseId: z.string().min(1, 'El ID del caso es requerido').optional(),
});

export type UpdateDocumentData = z.infer<typeof updateDocumentSchema>;

export const deleteDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
});

export type DeleteDocumentData = z.infer<typeof deleteDocumentSchema>;

export const getDocumentsSchema = z.array(documentWithCaseSchema);
export const getDocumentByIdSchema = documentWithCaseSchema;
export const createDocumentOutputSchema = documentWithCaseSchema;
export const updateDocumentOutputSchema = documentWithCaseSchema;
export const deleteDocumentOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
});
