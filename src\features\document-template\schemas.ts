import { z } from 'zod';

export const templatePlaceholderSchema = z.object({
  id: z.string().optional(),
  placeholder: z.string().min(1, 'El marcador es requerido'),
  fieldName: z.string().min(1, 'El nombre del campo es requerido'),
  description: z.string().min(1, 'La descripción es requerida'),
  dataType: z.enum(['text', 'number', 'date', 'boolean']).default('text'),
  isRequired: z.boolean().default(false),
  defaultValue: z.string().optional(),
  validationRule: z.string().optional(),
});

export const documentTemplateSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'El nombre es requerido'),
  description: z.string().optional(),
  category: z.string().min(1, 'La categoría es requerida'),
  subcategory: z.string().optional(),
  fileName: z.string().min(1, 'El nombre del archivo es requerido'),
  filePath: z.string().min(1, 'La ruta del archivo es requerida'),
  fileSize: z.number().min(1, 'El tamaño del archivo debe ser mayor a 0'),
  mimeType: z.string().default('application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
  isActive: z.boolean().default(true),
  placeholders: z.array(templatePlaceholderSchema).optional(),
});

export const uploadTemplateSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  description: z.string().optional(),
  category: z.string().min(1, 'La categoría es requerida'),
  subcategory: z.string().optional(),
  file: z.any().refine((file) => file instanceof File, 'Debe seleccionar un archivo'),
});

export const generateDocumentSchema = z.object({
  templateId: z.string().min(1, 'El ID de la plantilla es requerido'),
  caseId: z.string().optional(),
  name: z.string().min(1, 'El nombre del documento es requerido'),
  format: z.enum(['docx', 'pdf']).default('docx'),
  data: z.record(z.any()),
});

export const templateFilterSchema = z.object({
  category: z.string().optional(),
  subcategory: z.string().optional(),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
});

export const updateTemplateSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  description: z.string().optional(),
  category: z.string().min(1, 'La categoría es requerida').optional(),
  subcategory: z.string().optional(),
  isActive: z.boolean().optional(),
  placeholders: z.array(templatePlaceholderSchema).optional(),
});

export const deleteTemplateSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
});

export const templateVersionSchema = z.object({
  id: z.string().optional(),
  templateId: z.string().min(1, 'El ID de la plantilla es requerido'),
  version: z.string().min(1, 'La versión es requerida'),
  filePath: z.string().min(1, 'La ruta del archivo es requerida'),
  changelog: z.string().optional(),
});

export const generatedDocumentSchema = z.object({
  id: z.string().optional(),
  templateId: z.string().min(1, 'El ID de la plantilla es requerido'),
  caseId: z.string().optional(),
  name: z.string().min(1, 'El nombre es requerido'),
  filePath: z.string().min(1, 'La ruta del archivo es requerida'),
  format: z.enum(['docx', 'pdf']).default('docx'),
  status: z.enum(['GENERATED', 'DOWNLOADED', 'SENT']).default('GENERATED'),
  createdBy: z.string().min(1, 'El creador es requerido'),
});

export const placeholderDataSchema = z.object({
  DEBTOR_NAME: z.string().optional(),
  CASE_NUMBER: z.string().optional(),
  CASE_DATE: z.string().optional(),
  HEARING_DATE: z.string().optional(),
  HEARING_TIME: z.string().optional(),
  OPERATOR_NAME: z.string().optional(),
  CREDITOR_NAME: z.string().optional(),
  DEBT_AMOUNT: z.string().optional(),
  PAYMENT_TERMS: z.string().optional(),
  MONTHLY_AMOUNT: z.string().optional(),
  ZOOM_ID: z.string().optional(),
  ZOOM_PASSWORD: z.string().optional(),
  ATTORNEY_NAME: z.string().optional(),
  ID_NUMBER: z.string().optional(),
  ADDRESS: z.string().optional(),
  PHONE: z.string().optional(),
  EMAIL: z.string().optional(),
  CITY: z.string().optional(),
  DEPARTMENT: z.string().optional(),
});

export const templateStatsSchema = z.object({
  totalTemplates: z.number(),
  activeTemplates: z.number(),
  totalUsage: z.number(),
  categoriesCount: z.number(),
  recentlyUsed: z.array(z.object({
    id: z.string(),
    name: z.string(),
    usage: z.number(),
    lastUsed: z.date(),
  })),
});

export const bulkUploadSchema = z.object({
  category: z.string().min(1, 'La categoría es requerida'),
  files: z.array(z.any()).min(1, 'Debe seleccionar al menos un archivo'),
  preserveStructure: z.boolean().default(true),
});
