'use client';

import { useState, useEffect } from 'react';
import { FileText, Download, Eye, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { PlaceholderInput } from '@/components/ui/placeholder-input';
import {
  PlaceholderDefinition,
  PlaceholderValueType,
  PlaceholderValue,
  fillPlaceholders,
  validatePlaceholderValue,
} from '@/lib/placeholder-utils';

interface Template {
  id: string;
  name: string;
  description: string | null;
  content: string;
  category: string;
  subcategory: string | null;
  placeholders?: PlaceholderDefinition[];
}

interface TemplateFillDialogProps {
  readonly template: Template | null;
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onGenerate?: (filledContent: string, templateName: string) => void;
}

export function TemplateFillDialog({
  template,
  open,
  onOpenChange,
  onGenerate,
}: TemplateFillDialogProps) {
  const [values, setValues] = useState<Record<string, PlaceholderValueType>>(
    {},
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [showPreview, setShowPreview] = useState(false);

  // Reset form when template changes
  useEffect(() => {
    if (template?.placeholders) {
      const initialValues: Record<string, PlaceholderValueType> = {};
      template.placeholders.forEach((placeholder) => {
        initialValues[placeholder.fieldName] = placeholder.defaultValue || '';
      });
      setValues(initialValues);
      setErrors({});
      setShowPreview(false);
      setPreviewContent('');
    }
  }, [template]);

  const handleValueChange = (
    fieldName: string,
    value: PlaceholderValueType,
  ) => {
    setValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }));

    // Clear error for this field
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    if (!template?.placeholders) return true;

    const newErrors: Record<string, string> = {};
    let isValid = true;

    template.placeholders.forEach((placeholder) => {
      const value = values[placeholder.fieldName];
      const validation = validatePlaceholderValue(value, placeholder);

      if (!validation.isValid) {
        newErrors[placeholder.fieldName] =
          validation.error || 'Error de validación';
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handlePreview = () => {
    if (!template || !validateForm()) return;

    const placeholderValues: PlaceholderValue[] =
      template.placeholders?.map((def) => ({
        fieldName: def.fieldName,
        value: values[def.fieldName] ?? def.defaultValue ?? '',
      })) || [];

    const filledContent = fillPlaceholders(template.content, placeholderValues);
    setPreviewContent(filledContent);
    setShowPreview(true);
  };

  const handleGenerate = async () => {
    if (!template || !validateForm()) return;

    setIsGenerating(true);
    try {
      const placeholderValues: PlaceholderValue[] =
        template.placeholders?.map((def) => ({
          fieldName: def.fieldName,
          value: values[def.fieldName] ?? def.defaultValue ?? '',
        })) || [];

      const filledContent = fillPlaceholders(
        template.content,
        placeholderValues,
      );

      // Call the onGenerate callback
      onGenerate?.(filledContent, template.name);

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Error generating document:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadPreview = () => {
    if (!previewContent || !template) return;

    const blob = new Blob([previewContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${template.name}_generado.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      Inicial: 'bg-blue-100 text-blue-800',
      Admisión: 'bg-green-100 text-green-800',
      Notificación: 'bg-yellow-100 text-yellow-800',
      Suspensión: 'bg-orange-100 text-orange-800',
      Calificación: 'bg-purple-100 text-purple-800',
      Acuerdos: 'bg-emerald-100 text-emerald-800',
      Incumplimientos: 'bg-red-100 text-red-800',
      Reformas: 'bg-indigo-100 text-indigo-800',
      Rechazos: 'bg-gray-100 text-gray-800',
      Desistimientos: 'bg-pink-100 text-pink-800',
      Finalización: 'bg-slate-100 text-slate-800',
      Audiencias: 'bg-cyan-100 text-cyan-800',
      Fracasos: 'bg-rose-100 text-rose-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] w-[95vw] max-w-4xl">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-blue-600" />
              <div>
                <DialogTitle className="text-xl">
                  Completar Plantilla
                </DialogTitle>
                <DialogDescription>{template.name}</DialogDescription>
              </div>
            </div>
            <Badge className={getCategoryColor(template.category)}>
              {template.category}
            </Badge>
          </div>
        </DialogHeader>

        {!showPreview ? (
          // Form View
          <div className="flex-1">
            <ScrollArea className="h-[500px] pr-4">
              <div className="space-y-6">
                {template.description && (
                  <div>
                    <h3 className="mb-2 text-sm font-medium text-gray-700">
                      Descripción
                    </h3>
                    <p className="rounded-md bg-gray-50 p-3 text-sm text-gray-600">
                      {template.description}
                    </p>
                  </div>
                )}

                <Separator />

                <div>
                  <h3 className="mb-4 text-lg font-medium">
                    Información Requerida
                  </h3>
                  {template.placeholders && template.placeholders.length > 0 ? (
                    <div className="space-y-4">
                      {template.placeholders.map((placeholder) => (
                        <PlaceholderInput
                          key={placeholder.fieldName}
                          definition={placeholder}
                          value={values[placeholder.fieldName] || ''}
                          onChange={(value) =>
                            handleValueChange(placeholder.fieldName, value)
                          }
                          error={errors[placeholder.fieldName]}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="py-8 text-center">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-4 text-lg font-medium">
                        Sin marcadores
                      </h3>
                      <p className="text-gray-500">
                        Esta plantilla no requiere información adicional
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </div>
        ) : (
          // Preview View
          <div className="flex-1">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium">
                Vista Previa del Documento
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadPreview}
              >
                <Download className="mr-2 h-4 w-4" />
                Descargar
              </Button>
            </div>
            <ScrollArea className="h-[500px] w-full rounded-md border p-6">
              <div className="prose prose-sm prose-headings:text-gray-900 prose-p:text-gray-700 prose-strong:text-gray-900 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:text-gray-700 max-w-none">
                <pre className="font-sans text-sm leading-relaxed whitespace-pre-wrap">
                  {previewContent}
                </pre>
              </div>
            </ScrollArea>
          </div>
        )}

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            {!showPreview &&
              template.placeholders &&
              template.placeholders.length > 0 && (
                <Button
                  variant="outline"
                  onClick={handlePreview}
                  disabled={Object.keys(errors).length > 0}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  Vista Previa
                </Button>
              )}
            {showPreview && (
              <Button variant="outline" onClick={() => setShowPreview(false)}>
                Volver al Formulario
              </Button>
            )}
          </div>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || Object.keys(errors).length > 0}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generar Documento
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
