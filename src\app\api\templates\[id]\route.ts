import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const template = await prisma.template.findUnique({
      where: { id },
      include: {
        placeholders: true,
        versions: {
          orderBy: { createdDate: 'desc' },
        },
        _count: {
          select: {
            documents: true,
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 },
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch template' },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      name,
      description,
      content,
      category,
      subcategory,
      placeholders = [],
      createVersion = false,
      changelog,
    } = body;

    const template = await prisma.template.update({
      where: { id },
      data: {
        name,
        description,
        content,
        category,
        subcategory,
        updatedDate: new Date(),
        // Delete existing placeholders and create new ones
        placeholders: {
          deleteMany: {},
          create: placeholders,
        },
        // Create new version if requested
        ...(createVersion && {
          versions: {
            create: {
              version: new Date().toISOString().split('T')[0], // Use date as version
              content,
              changelog: changelog || 'Template updated',
            },
          },
        }),
      },
      include: {
        placeholders: true,
        versions: {
          orderBy: { createdDate: 'desc' },
          take: 1,
        },
      },
    });

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error updating template:', error);
    return NextResponse.json(
      { error: 'Failed to update template' },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    // Soft delete by setting isActive to false
    await prisma.template.update({
      where: { id },
      data: { isActive: false },
    });

    return NextResponse.json({ message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 },
    );
  }
}
