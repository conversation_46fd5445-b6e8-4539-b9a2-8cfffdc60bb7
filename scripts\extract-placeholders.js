#!/usr/bin/env node

/**
 * <PERSON>ript to extract placeholders from existing templates and update the database
 */

import { PrismaClient } from '@prisma/client';
import {
  extractPlaceholders,
  generatePlaceholderDefinitions,
} from '../src/lib/placeholder-utils.js';

const prisma = new PrismaClient();

async function extractPlaceholdersFromTemplates() {
  try {
    console.log('Starting placeholder extraction...');

    // Get all templates
    const templates = await prisma.template.findMany({
      include: {
        placeholders: true,
      },
    });

    console.log(`Found ${templates.length} templates to process`);

    let totalPlaceholdersExtracted = 0;

    for (const template of templates) {
      console.log(`\nProcessing template: ${template.name}`);

      // Extract placeholders from content
      const placeholderKeys = extractPlaceholders(template.content);
      console.log(
        `Found ${placeholderKeys.length} placeholders: ${placeholderKeys.join(', ')}`,
      );

      if (placeholderKeys.length === 0) {
        console.log('No placeholders found, skipping...');
        continue;
      }

      // Generate placeholder definitions
      const definitions = generatePlaceholderDefinitions(placeholderKeys);

      // Check which placeholders already exist
      const existingPlaceholders = template.placeholders.map(
        (p) => p.fieldName,
      );
      const newDefinitions = definitions.filter(
        (def) => !existingPlaceholders.includes(def.fieldName),
      );

      if (newDefinitions.length === 0) {
        console.log('All placeholders already exist, skipping...');
        continue;
      }

      console.log(`Adding ${newDefinitions.length} new placeholders`);

      // Create new placeholder records
      for (const definition of newDefinitions) {
        await prisma.templatePlaceholder.create({
          data: {
            templateId: template.id,
            placeholder: definition.placeholder,
            fieldName: definition.fieldName,
            description: definition.description,
            dataType: definition.dataType,
            isRequired: definition.isRequired,
            defaultValue: definition.defaultValue,
          },
        });
        console.log(
          `  ✅ Added: ${definition.placeholder} (${definition.dataType})`,
        );
        totalPlaceholdersExtracted++;
      }
    }

    console.log(`\n🎉 Extraction complete!`);
    console.log(`Total new placeholders added: ${totalPlaceholdersExtracted}`);
  } catch (error) {
    console.error('Error extracting placeholders:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
extractPlaceholdersFromTemplates();
