import { promises as fs } from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';

export class FileStorage {
  private static readonly STORAGE_ROOT = process.env.STORAGE_PATH || './storage';
  private static readonly TEMPLATES_DIR = 'templates';
  private static readonly GENERATED_DIR = 'generated';

  static async ensureDirectories() {
    const templatesPath = path.join(this.STORAGE_ROOT, this.TEMPLATES_DIR);
    const generatedPath = path.join(this.STORAGE_ROOT, this.GENERATED_DIR);
    
    await fs.mkdir(templatesPath, { recursive: true });
    await fs.mkdir(generatedPath, { recursive: true });
  }

  static async saveTemplate(file: File, category: string): Promise<string> {
    await this.ensureDirectories();
    
    const fileExtension = path.extname(file.name);
    const fileName = `${randomUUID()}${fileExtension}`;
    const categoryPath = path.join(this.STORAGE_ROOT, this.TEMPLATES_DIR, category);
    
    await fs.mkdir(categoryPath, { recursive: true });
    
    const filePath = path.join(categoryPath, fileName);
    const buffer = Buffer.from(await file.arrayBuffer());
    
    await fs.writeFile(filePath, buffer);
    
    return path.relative(this.STORAGE_ROOT, filePath);
  }

  static async saveGenerated(buffer: Buffer, fileName: string): Promise<string> {
    await this.ensureDirectories();
    
    const filePath = path.join(this.STORAGE_ROOT, this.GENERATED_DIR, fileName);
    await fs.writeFile(filePath, buffer);
    
    return path.relative(this.STORAGE_ROOT, filePath);
  }

  static async getFile(relativePath: string): Promise<Buffer> {
    const fullPath = path.join(this.STORAGE_ROOT, relativePath);
    return await fs.readFile(fullPath);
  }

  static async deleteFile(relativePath: string): Promise<void> {
    const fullPath = path.join(this.STORAGE_ROOT, relativePath);
    await fs.unlink(fullPath);
  }

  static async fileExists(relativePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.STORAGE_ROOT, relativePath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  static getFullPath(relativePath: string): string {
    return path.join(this.STORAGE_ROOT, relativePath);
  }

  static async getFileStats(relativePath: string) {
    const fullPath = path.join(this.STORAGE_ROOT, relativePath);
    return await fs.stat(fullPath);
  }
}

export interface UploadedFile {
  name: string;
  size: number;
  type: string;
  buffer: Buffer;
}

export function validateWordDocument(file: File): boolean {
  const validMimeTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ];
  
  const validExtensions = ['.docx', '.doc'];
  const fileExtension = path.extname(file.name).toLowerCase();
  
  return validMimeTypes.includes(file.type) && validExtensions.includes(fileExtension);
}

export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '');
}

export function getCategoryFromPath(folderPath: string): { category: string; subcategory?: string } {
  const parts = folderPath.split('/').filter(Boolean);
  
  if (parts.length === 1) {
    return { category: parts[0] };
  }
  
  return {
    category: parts[0],
    subcategory: parts.slice(1).join('/')
  };
}
