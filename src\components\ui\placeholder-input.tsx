'use client';

import { useState } from 'react';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import {
  PlaceholderDefinition,
  PlaceholderValueType,
  validatePlaceholderValue,
} from '@/lib/placeholder-utils';

interface PlaceholderInputProps {
  readonly definition: PlaceholderDefinition;
  readonly value: PlaceholderValueType;
  readonly onChange: (value: PlaceholderValueType) => void;
  readonly error?: string;
}

export function PlaceholderInput({
  definition,
  value,
  onChange,
  error,
}: PlaceholderInputProps) {
  const [internalError, setInternalError] = useState<string | undefined>();

  const handleChange = (newValue: PlaceholderValueType) => {
    // Clear internal error when value changes
    setInternalError(undefined);

    // Validate the new value
    const validation = validatePlaceholderValue(newValue, definition);
    if (!validation.isValid) {
      setInternalError(validation.error);
    }

    onChange(newValue);
  };

  const displayError = error || internalError;

  const renderInput = () => {
    switch (definition.dataType) {
      case 'text':
        return (
          <Input
            type="text"
            value={value?.toString() || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={
              definition.defaultValue ||
              `Ingrese ${definition.description.toLowerCase()}`
            }
            className={cn(displayError && 'border-red-500')}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value?.toString() || ''}
            onChange={(e) =>
              handleChange(e.target.value ? parseFloat(e.target.value) : '')
            }
            placeholder={definition.defaultValue || '0'}
            min={definition.validation?.min}
            max={definition.validation?.max}
            className={cn(displayError && 'border-red-500')}
          />
        );

      case 'currency':
        return (
          <div className="relative">
            <span className="absolute top-1/2 left-3 -translate-y-1/2 text-sm text-gray-500">
              $
            </span>
            <Input
              type="number"
              value={value?.toString() || ''}
              onChange={(e) =>
                handleChange(e.target.value ? parseFloat(e.target.value) : '')
              }
              placeholder="0"
              min={definition.validation?.min || 0}
              max={definition.validation?.max}
              className={cn('pl-8', displayError && 'border-red-500')}
            />
          </div>
        );

      case 'email':
        return (
          <Input
            type="email"
            value={value?.toString() || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={definition.defaultValue || '<EMAIL>'}
            className={cn(displayError && 'border-red-500')}
          />
        );

      case 'date':
        return (
          <DatePicker
            value={value?.toString() || ''}
            onChange={handleChange}
            defaultValue={definition.defaultValue}
            error={!!displayError}
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={definition.fieldName}
              checked={Boolean(value)}
              onCheckedChange={(checked) => handleChange(Boolean(checked))}
            />
            <label
              htmlFor={definition.fieldName}
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {definition.description}
            </label>
          </div>
        );

      case 'select':
        return (
          <Select value={value?.toString() || ''} onValueChange={handleChange}>
            <SelectTrigger className={cn(displayError && 'border-red-500')}>
              <SelectValue
                placeholder={`Seleccione ${definition.description.toLowerCase()}`}
              />
            </SelectTrigger>
            <SelectContent>
              {definition.validation?.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      default:
        return (
          <Textarea
            value={value?.toString() || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={
              definition.defaultValue ||
              `Ingrese ${definition.description.toLowerCase()}`
            }
            className={cn(displayError && 'border-red-500')}
            rows={3}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={definition.fieldName} className="flex items-center gap-1">
        {definition.description}
        {definition.isRequired && <span className="text-red-500">*</span>}
      </Label>
      {definition.dataType !== 'boolean' && renderInput()}
      {definition.dataType === 'boolean' && renderInput()}
      {displayError && <p className="text-sm text-red-500">{displayError}</p>}
      {definition.validation?.pattern && definition.dataType === 'text' && (
        <p className="text-xs text-gray-500">
          Formato requerido: {definition.validation.pattern}
        </p>
      )}
    </div>
  );
}

interface DatePickerProps {
  readonly value: string;
  readonly onChange: (value: string) => void;
  readonly defaultValue?: string;
  readonly error?: boolean;
}

function DatePicker({ value, onChange, defaultValue, error }: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getDateValue = (): Date | undefined => {
    if (value === 'today' || defaultValue === 'today') {
      return new Date();
    }
    if (value) {
      const date = new Date(value);
      return isNaN(date.getTime()) ? undefined : date;
    }
    return undefined;
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      onChange(format(date, 'yyyy-MM-dd'));
      setIsOpen(false);
    }
  };

  const handleTodayClick = () => {
    onChange('today');
    setIsOpen(false);
  };

  const displayValue = () => {
    if (value === 'today') {
      return 'Fecha actual';
    }
    const date = getDateValue();
    return date ? format(date, 'PPP', { locale: es }) : '';
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-full justify-start text-left font-normal',
            !value && 'text-muted-foreground',
            error && 'border-red-500',
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {displayValue() || 'Seleccione una fecha'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="border-b p-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTodayClick}
            className="w-full"
          >
            Usar fecha actual
          </Button>
        </div>
        <Calendar
          mode="single"
          selected={getDateValue()}
          onSelect={handleDateSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
