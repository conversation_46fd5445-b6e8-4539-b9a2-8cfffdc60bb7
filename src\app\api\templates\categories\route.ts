import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const categories = await prisma.template.findMany({
      where: { isActive: true },
      select: {
        category: true,
        subcategory: true,
      },
      distinct: ['category', 'subcategory'],
      orderBy: [{ category: 'asc' }, { subcategory: 'asc' }],
    });

    // Group categories with their subcategories
    const groupedCategories = categories.reduce((acc: any, curr) => {
      if (!acc[curr.category]) {
        acc[curr.category] = {
          name: curr.category,
          subcategories: [],
        };
      }

      if (
        curr.subcategory &&
        !acc[curr.category].subcategories.includes(curr.subcategory)
      ) {
        acc[curr.category].subcategories.push(curr.subcategory);
      }

      return acc;
    }, {});

    return NextResponse.json(Object.values(groupedCategories));
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 },
    );
  }
}
