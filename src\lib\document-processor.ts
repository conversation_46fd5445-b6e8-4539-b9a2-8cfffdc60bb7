import Docxtemplater from 'docxtemplater';
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import { FileStorage } from './storage';

export interface PlaceholderData {
  [key: string]: string | number | boolean | Date;
}

export interface DocumentPlaceholder {
  placeholder: string;
  fieldName: string;
  description: string;
  dataType: 'text' | 'number' | 'date' | 'boolean';
  isRequired: boolean;
  defaultValue?: string;
}

export class DocumentProcessor {
  static async extractPlaceholders(templatePath: string): Promise<DocumentPlaceholder[]> {
    try {
      const buffer = await FileStorage.getFile(templatePath);
      const zip = new PizZip(buffer);
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      const placeholders: DocumentPlaceholder[] = [];
      const placeholderRegex = /\{\{([^}]+)\}\}/g;
      
      const xmlContent = zip.files['word/document.xml']?.asText();
      if (!xmlContent) {
        throw new Error('No se pudo leer el contenido del documento');
      }

      let match;
      const foundPlaceholders = new Set<string>();

      while ((match = placeholderRegex.exec(xmlContent)) !== null) {
        const placeholder = match[0];
        const fieldName = match[1].trim();
        
        if (!foundPlaceholders.has(fieldName)) {
          foundPlaceholders.add(fieldName);
          
          placeholders.push({
            placeholder,
            fieldName,
            description: this.generateDescription(fieldName),
            dataType: this.inferDataType(fieldName),
            isRequired: true,
            defaultValue: undefined,
          });
        }
      }

      return placeholders;
    } catch (error) {
      console.error('Error extracting placeholders:', error);
      throw new Error('Error al extraer marcadores de posición del documento');
    }
  }

  static async generateDocument(
    templatePath: string,
    data: PlaceholderData,
    outputFileName: string
  ): Promise<string> {
    try {
      const buffer = await FileStorage.getFile(templatePath);
      const zip = new PizZip(buffer);
      
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      doc.render(data);

      const generatedBuffer = doc.getZip().generate({
        type: 'nodebuffer',
        compression: 'DEFLATE',
      });

      const outputPath = await FileStorage.saveGenerated(
        generatedBuffer,
        outputFileName
      );

      return outputPath;
    } catch (error) {
      console.error('Error generating document:', error);
      throw new Error('Error al generar el documento');
    }
  }

  static async validateTemplate(templatePath: string): Promise<boolean> {
    try {
      const buffer = await FileStorage.getFile(templatePath);
      const zip = new PizZip(buffer);
      
      new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      return true;
    } catch (error) {
      console.error('Template validation failed:', error);
      return false;
    }
  }

  private static generateDescription(fieldName: string): string {
    const descriptions: Record<string, string> = {
      DEBTOR_NAME: 'Nombre del deudor',
      CASE_NUMBER: 'Número del caso',
      CASE_DATE: 'Fecha del caso',
      HEARING_DATE: 'Fecha de audiencia',
      HEARING_TIME: 'Hora de audiencia',
      OPERATOR_NAME: 'Nombre del operador',
      CREDITOR_NAME: 'Nombre del acreedor',
      DEBT_AMOUNT: 'Monto de la deuda',
      PAYMENT_TERMS: 'Términos de pago',
      MONTHLY_AMOUNT: 'Monto mensual',
      ZOOM_ID: 'ID de Zoom',
      ZOOM_PASSWORD: 'Contraseña de Zoom',
      ATTORNEY_NAME: 'Nombre del apoderado',
      ID_NUMBER: 'Número de identificación',
      ADDRESS: 'Dirección',
      PHONE: 'Teléfono',
      EMAIL: 'Correo electrónico',
      CITY: 'Ciudad',
      DEPARTMENT: 'Departamento',
    };

    return descriptions[fieldName] || fieldName.replace(/_/g, ' ').toLowerCase();
  }

  private static inferDataType(fieldName: string): 'text' | 'number' | 'date' | 'boolean' {
    const dateFields = ['DATE', 'FECHA'];
    const numberFields = ['AMOUNT', 'MONTO', 'NUMBER', 'NUMERO'];
    const booleanFields = ['IS_', 'HAS_', 'ACTIVE', 'ENABLED'];

    const upperFieldName = fieldName.toUpperCase();

    if (dateFields.some(field => upperFieldName.includes(field))) {
      return 'date';
    }

    if (numberFields.some(field => upperFieldName.includes(field))) {
      return 'number';
    }

    if (booleanFields.some(field => upperFieldName.includes(field))) {
      return 'boolean';
    }

    return 'text';
  }

  static formatDataForTemplate(data: PlaceholderData): PlaceholderData {
    const formatted: PlaceholderData = {};

    for (const [key, value] of Object.entries(data)) {
      if (value instanceof Date) {
        formatted[key] = value.toLocaleDateString('es-CO');
      } else if (typeof value === 'number') {
        formatted[key] = value.toLocaleString('es-CO');
      } else if (typeof value === 'boolean') {
        formatted[key] = value ? 'Sí' : 'No';
      } else {
        formatted[key] = String(value || '');
      }
    }

    return formatted;
  }

  static async previewDocument(templatePath: string, data: PlaceholderData): Promise<string> {
    try {
      const buffer = await FileStorage.getFile(templatePath);
      const zip = new PizZip(buffer);
      
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      doc.render(data);

      const xmlContent = zip.files['word/document.xml']?.asText();
      if (!xmlContent) {
        throw new Error('No se pudo generar la vista previa');
      }

      return this.extractTextFromXml(xmlContent);
    } catch (error) {
      console.error('Error generating preview:', error);
      throw new Error('Error al generar la vista previa del documento');
    }
  }

  private static extractTextFromXml(xml: string): string {
    return xml
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 500) + '...';
  }
}
