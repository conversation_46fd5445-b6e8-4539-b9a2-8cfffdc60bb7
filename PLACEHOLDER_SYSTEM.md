# Placeholder System Implementation

## Overview

This system allows users to create dynamic documents by filling placeholders in templates. The placeholders use the `{{fieldName}}` syntax and can be of different data types with validation.

## Features Implemented

### 1. Placeholder Utilities (`src/lib/placeholder-utils.ts`)

- **Placeholder extraction**: Automatically scans template content for `{{placeholder}}` patterns
- **Type inference**: Automatically determines data types based on field names (date, currency, email, etc.)
- **Validation**: Comprehensive validation for different data types
- **Value formatting**: Automatic formatting for dates, currency, booleans, etc.

### 2. Placeholder Input Component (`src/components/ui/placeholder-input.tsx`)

- **Dynamic form inputs**: Renders appropriate input types based on data type
- **Date picker**: Special date picker with "today" option
- **Currency input**: Currency formatting with peso symbol
- **Boolean checkbox**: Yes/No checkboxes
- **Select dropdown**: Configurable dropdown options
- **Real-time validation**: Validates input as user types

### 3. Template Fill Dialog (`src/app/documents/components/template-fill-dialog.tsx`)

- **Form generation**: Automatically generates forms from placeholder definitions
- **Preview mode**: Show filled document before generating
- **Error handling**: Shows validation errors and prevents submission
- **Document generation**: Creates filled documents ready for download

### 4. Template Library Integration

- **Create button**: New "Crear" button on each template card
- **Full workflow**: From template selection to filled document generation
- **Download functionality**: Generate and download filled documents

### 5. Database Schema

- **TemplatePlaceholder model**: Stores placeholder definitions
- **Validation rules**: Support for min/max, patterns, options
- **Default values**: Support for common defaults like "today", "currentYear"

### 6. Extraction Script (`scripts/extract-placeholders-fixed.js`)

- **Automatic extraction**: Scans existing templates for placeholders
- **Smart type detection**: Infers types based on Spanish field names
- **Batch processing**: Processes all templates at once

## Supported Data Types

1. **text**: Regular text input with length validation
2. **number**: Numeric input with min/max validation
3. **currency**: Currency input with peso formatting
4. **date**: Date picker with Spanish locale
5. **email**: Email input with validation
6. **boolean**: Checkbox for yes/no values
7. **select**: Dropdown with predefined options

## Placeholder Syntax

Templates use the `{{fieldName}}` syntax for placeholders:

```markdown
**DEUDOR: {{nombreDeudor}} CC. {{cedulaDeudor}}**

Fecha de audiencia: {{fechaAudiencia}}
Monto de la deuda: {{montoDeuda}}
```

## Smart Type Inference

The system automatically infers data types based on Spanish field names:

- **fechaAudiencia** → `date`
- **montoDeuda** → `currency`
- **cedulaDeudor** → `number`
- **correoElectronico** → `email`
- **tieneActivos** → `boolean`

## Validation Features

- **Required fields**: Mark fields as mandatory
- **Length limits**: Min/max character limits for text
- **Number ranges**: Min/max values for numbers
- **Pattern matching**: Regular expressions for format validation
- **Email format**: Automatic email validation
- **Date validation**: Valid date selection

## Default Values

Support for special default values:

- `today`: Current date
- `currentYear`: Current year
- Custom defaults per field

## Value Formatting

Automatic formatting for display:

- **Dates**: "14 de julio de 2025" (Spanish format)
- **Currency**: "$1.500.000 COP" (Colombian pesos)
- **Boolean**: "Sí" / "No" (Spanish)

## Usage Workflow

1. **Select Template**: User chooses a template from the library
2. **Click "Crear"**: Opens the template fill dialog
3. **Fill Form**: User fills in the required information
4. **Preview**: User can preview the filled document
5. **Generate**: System creates the final document
6. **Download**: User downloads the completed document

## Technical Implementation

### Frontend Components

- `PlaceholderInput`: Renders appropriate input for each data type
- `TemplateFillDialog`: Main dialog for filling templates
- `DocumentTemplateLibrary`: Updated with "Crear" functionality

### Backend Processing

- Placeholder extraction from template content
- Database storage of placeholder definitions
- API endpoints for template management

### Database Structure

```sql
template_placeholders:
  - id
  - templateId
  - placeholder (e.g., "{{nombreDeudor}}")
  - fieldName (e.g., "nombreDeudor")
  - description (e.g., "Nombre del Deudor")
  - dataType (text, number, date, currency, etc.)
  - isRequired
  - defaultValue
```

## Example Template

```markdown
**NOTIFICACIÓN A ACREEDOR**

**Señor {{nombreAcreedor}}**

**DEUDOR: {{nombreDeudor}} CC. {{cedulaDeudor}}**

Sírvase comparecer el {{fechaAudiencia}} a las {{horaAudiencia}}
para la audiencia de negociación de deudas.

Monto adeudado: {{montoDeuda}}
```

## Testing

The system has been tested with:

- Template "Notificacion A Los Acreedores" (ID: cmd3dz60p0024cx088xv9lecy)
- 5 placeholders extracted: nombreAcreedor, nombreDeudor, cedulaDeudor, fechaAudiencia, horaAudiencia
- Complete workflow from form filling to document generation

## Future Enhancements

1. **Conditional placeholders**: Show/hide fields based on other values
2. **Calculated fields**: Auto-calculate values based on formulas
3. **Template inheritance**: Reuse placeholder definitions across templates
4. **Bulk generation**: Generate multiple documents at once
5. **PDF generation**: Direct PDF output instead of markdown
6. **Template versioning**: Track changes to placeholder definitions
7. **User templates**: Allow users to create custom templates
8. **Integration with case data**: Auto-fill from existing case information

## Performance Considerations

- Placeholders are cached in the database
- Form validation happens client-side
- Large templates are paginated
- Async placeholder extraction for imports

## Security

- Input validation on both client and server
- SQL injection prevention through Prisma ORM
- XSS protection through React's built-in escaping
- File download security through blob URLs

This system provides a complete, user-friendly solution for creating dynamic legal documents with automatic placeholder detection, intelligent form generation, and robust validation.
