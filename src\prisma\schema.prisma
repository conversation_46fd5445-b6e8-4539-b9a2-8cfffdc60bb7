generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "cockroachdb"
  url      = env("DATABASE_URL")
}

model Case {
  id          String    @id @default(cuid())
  caseNumber  String    @unique
  debtorName  String
  type        String
  status      String
  totalDebt   Decimal
  creditors   Int
  createdDate DateTime  @default(now())
  hearingDate DateTime?
  phase       String?
  causes      String[]  @default([])
  debtorId    String
  operatorId  String

  // New required fields
  tramite                String? // Trámite
  filingDate             DateTime? // Fecha de radicación
  debtorIdNumber         String? // Cédula
  convened               Boolean   @default(false) // Convocado
  attorney               String? // Apoderado
  owedCapital            Decimal? // Capital adeudado
  designatedOperator     String? // Operador designado
  designationDate        DateTime? // Fecha designación
  positionAcceptanceDate DateTime? // Fecha aceptación cargo
  inadmissionDate        DateTime? // Fecha de inadmision
  admissionDate          DateTime? // Fecha de admisión
  firstHearingDate       DateTime? // Fecha de primera audiencia
  firstHearingTime       String? // Hora de primera audiencia
  rejection              Boolean   @default(false) // Rechazo
  withdrawal             Boolean   @default(false) // Desiste
  hasLegalProcesses      Boolean   @default(false) // Tiene procesos en contra

  // Legal process details
  courtNumber        String? // Número juzgado
  city               String? // Ciudad
  processType        String? // Tipo proceso
  plaintiff          String? // Demandante
  judicialFileNumber String? // No. radicado judicial
  suspensionDate     DateTime? // Fecha de suspensión
  resultDeliveryDate DateTime? // Fecha de entrega de resultado

  // Result information
  resultType              String? // Resultado (fracaso/acuerdo)
  resultDate              DateTime? // Fecha de resultado
  siccacNumber            String? // Número de siccac
  riskCenterCommunication Boolean   @default(false) // Comunicación a central de riesgo

  assets         Asset[]
  debtor         Debtor         @relation(fields: [debtorId], references: [id])
  operator       User           @relation(fields: [operatorId], references: [id])
  debts          Debt[]
  documents      Document[]
  legalProcesses LegalProcess[]
}

model Document {
  id         String   @id @default(cuid())
  name       String
  type       String
  status     String
  uploadDate DateTime @default(now())
  url        String
  caseId     String
  case       Case     @relation(fields: [caseId], references: [id])
}

model Debtor {
  id               String    @id @default(cuid())
  name             String
  idNumber         String    @unique
  idType           String
  email            String    @unique
  phone            String
  address          String
  city             String
  department       String
  birthDate        DateTime?
  maritalStatus    String?
  occupation       String
  monthlyIncome    Decimal
  monthlyExpenses  Decimal?
  dependents       Int?
  educationLevel   String?
  totalDebt        Decimal
  status           String
  emergencyContact String?
  emergencyPhone   String?
  bankAccount      String?
  bankName         String?
  accountType      String?
  description      String?
  activeCases      Int
  createdDate      DateTime  @default(now())
  lastUpdate       DateTime  @updatedAt
  assets           Asset[]
  cases            Case[]
  debts            Debt[]
}

model Debt {
  id           String   @id @default(cuid())
  amount       Decimal
  interestRate Float
  type         String
  caseId       String
  creditorId   String
  debtorId     String?
  case         Case     @relation(fields: [caseId], references: [id])
  creditor     Creditor @relation(fields: [creditorId], references: [id])
  debtor       Debtor?  @relation(fields: [debtorId], references: [id])
}

model Asset {
  id       String  @id @default(cuid())
  name     String
  type     String
  value    Decimal
  caseId   String
  debtorId String?
  case     Case    @relation(fields: [caseId], references: [id])
  debtor   Debtor? @relation(fields: [debtorId], references: [id])
}

model Creditor {
  id                  String    @id @default(cuid())
  name                String
  type                String
  email               String    @unique
  phone               String
  address             String
  status              String
  representative      String
  nit                 String    @unique
  website             String?
  city                String?
  department          String?
  bankName            String?
  activeCases         Int       @default(0)
  createdDate         DateTime? @default(now())
  lastUpdate          DateTime? @updatedAt
  description         String?
  representativeId    String?
  representativeEmail String?
  representativePhone String?
  contacts            Contact[]
  debts               Debt[]
}

model Contact {
  id         String   @id @default(cuid())
  name       String
  role       String
  email      String
  phone      String
  creditorId String
  creditor   Creditor @relation(fields: [creditorId], references: [id])
}

model LegalProcess {
  id          String @id @default(cuid())
  description String
  caseId      String
  case        Case   @relation(fields: [caseId], references: [id])
}

model User {
  id               String        @id @default(cuid())
  name             String
  email            String        @unique
  status           String        @default("Activo")
  lastLogin        DateTime?
  professionalCard String
  phone            String
  address          String
  createdDate      DateTime      @default(now())
  roleId           String
  password         String? // Add password field for authentication
  activityLogs     ActivityLog[]
  assignedCases    Case[]
  role             Role          @relation(fields: [roleId], references: [id])
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  permissions String[]
  color       String   @default("bg-gray-100 text-gray-800")
  users       User[]
}

model ActivityLog {
  id     String   @id @default(cuid())
  date   DateTime @default(now())
  action String
  userId String
  user   User     @relation(fields: [userId], references: [id])
}

enum crdb_internal_region {
  aws_us_east_1 @map("aws-us-east-1")
}
