'use client';

import { useState } from 'react';
import { useAction } from 'zsa-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Upload, 
  FileText, 
  Search, 
  Filter, 
  Download,
  Edit,
  Trash2,
  Play,
  Eye,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { getTemplates, deleteTemplate } from '@/features/document-template/actions';
import { UploadTemplateDialog } from './components/upload-template-dialog';
import { GenerateDocumentDialog } from './components/generate-document-dialog';
import type { DocumentTemplateWithRelations } from '@/features/document-template/types';

export default function TemplatesPage() {
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplateWithRelations | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const { execute: executeGetTemplates, data: templatesData, isPending: isLoadingTemplates } = useAction(getTemplates, {
    onError: ({ err }) => {
      toast.error(err.message);
    },
  });

  const { execute: executeDeleteTemplate } = useAction(deleteTemplate, {
    onSuccess: () => {
      toast.success('Plantilla eliminada exitosamente');
      executeGetTemplates({});
    },
    onError: ({ err }) => {
      toast.error(err.message);
    },
  });

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm('¿Está seguro de que desea eliminar esta plantilla?')) {
      executeDeleteTemplate({ id: templateId });
    }
  };

  const handleGenerateDocument = (template: DocumentTemplateWithRelations) => {
    setSelectedTemplate(template);
    setShowGenerateDialog(true);
  };

  const handleUploadSuccess = () => {
    executeGetTemplates({});
  };

  const handleGenerateSuccess = () => {
    executeGetTemplates({});
  };

  const templates = templatesData?.data?.templates || [];
  const categories = templatesData?.data?.categories || [];
  const stats = templatesData?.data?.stats;

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchTerm || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = !selectedCategory || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Plantillas de Documentos</h1>
          <p className="text-gray-600 mt-2">
            Gestiona plantillas de Word para generar documentos automáticamente
          </p>
        </div>
        <Button onClick={() => setShowUploadDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Subir Plantilla
        </Button>
      </div>

      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Plantillas</p>
                  <p className="text-2xl font-bold">{stats.totalTemplates}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Upload className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Activas</p>
                  <p className="text-2xl font-bold">{stats.activeTemplates}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Download className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Documentos Generados</p>
                  <p className="text-2xl font-bold">{stats.totalUsage}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Filter className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Categorías</p>
                  <p className="text-2xl font-bold">{stats.categoriesCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar plantillas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Todas las categorías" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Todas las categorías</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.name} value={category.name}>
                {category.name} ({category.count})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button 
          variant="outline" 
          onClick={() => executeGetTemplates({})}
          disabled={isLoadingTemplates}
        >
          <Filter className="mr-2 h-4 w-4" />
          Actualizar
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    {template.description || 'Sin descripción'}
                  </p>
                </div>
                <Badge variant="secondary">{template.category}</Badge>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                {template.subcategory && (
                  <Badge variant="outline" className="text-xs">
                    {template.subcategory}
                  </Badge>
                )}
                
                <div className="text-sm text-gray-600">
                  <p>Marcadores: {template.placeholders?.length || 0}</p>
                  <p>Uso: {template.usage || 0} veces</p>
                  <p>Tamaño: {(template.fileSize / 1024).toFixed(1)} KB</p>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleGenerateDocument(template)}
                    className="flex-1"
                  >
                    <Play className="mr-1 h-3 w-3" />
                    Generar
                  </Button>
                  
                  <Button size="sm" variant="outline">
                    <Eye className="h-3 w-3" />
                  </Button>
                  
                  <Button size="sm" variant="outline">
                    <Edit className="h-3 w-3" />
                  </Button>
                  
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleDeleteTemplate(template.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && !isLoadingTemplates && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No hay plantillas disponibles
            </h3>
            <p className="text-gray-600 mb-4">
              Comience subiendo su primera plantilla de Word
            </p>
            <Button onClick={() => setShowUploadDialog(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Subir Primera Plantilla
            </Button>
          </CardContent>
        </Card>
      )}

      <UploadTemplateDialog
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
        onSuccess={handleUploadSuccess}
      />

      <GenerateDocumentDialog
        open={showGenerateDialog}
        onOpenChange={setShowGenerateDialog}
        template={selectedTemplate}
        onSuccess={handleGenerateSuccess}
      />
    </div>
  );
}
