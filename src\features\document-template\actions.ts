'use server';

import { createServerAction } from 'zsa';
import prisma from '@/lib/prisma';
import { revalidatePath } from 'next/cache';
import {
  uploadTemplateSchema,
  generateDocumentSchema,
  templateFilterSchema,
  updateTemplateSchema,
  deleteTemplateSchema,
} from './schemas';
import { FileStorage, validateWordDocument } from '@/lib/storage';
import { DocumentProcessor } from '@/lib/document-processor';
import type { TemplateSearchResult } from './types';

export const uploadTemplate = createServerAction()
  .input(uploadTemplateSchema)
  .handler(async ({ input }) => {
    try {
      const { name, description, category, subcategory, file } = input;

      if (!validateWordDocument(file)) {
        return {
          success: false,
          message: 'El archivo debe ser un documento de Word válido (.docx)',
          errors: { file: ['Formato de archivo inválido'] },
        };
      }

      const filePath = await FileStorage.saveTemplate(file, category);

      const placeholders =
        await DocumentProcessor.extractPlaceholders(filePath);

      const template = await prisma.documentTemplate.create({
        data: {
          name,
          description,
          category,
          subcategory,
          fileName: file.name,
          filePath,
          fileSize: file.size,
          mimeType: file.type,
          createdBy: 'current-user-id', // TODO: Get from session
          placeholders: {
            create: placeholders.map((p) => ({
              placeholder: p.placeholder,
              fieldName: p.fieldName,
              description: p.description,
              dataType: p.dataType,
              isRequired: p.isRequired,
              defaultValue: p.defaultValue,
            })),
          },
          versions: {
            create: {
              version: '1.0',
              filePath,
              changelog: 'Versión inicial de la plantilla',
            },
          },
        },
        include: {
          placeholders: true,
          versions: true,
        },
      });

      revalidatePath('/documents/templates');

      return {
        success: true,
        data: template,
        message: 'Plantilla subida exitosamente',
      };
    } catch (error) {
      console.error('Error uploading template:', error);
      return {
        success: false,
        message: 'Error al subir la plantilla',
        errors: { general: ['Error interno del servidor'] },
      };
    }
  });

export const getTemplates = createServerAction()
  .input(templateFilterSchema.optional())
  .handler(async ({ input: filters }) => {
    try {
      const where = {
        ...(filters?.category && { category: filters.category }),
        ...(filters?.subcategory && { subcategory: filters.subcategory }),
        ...(filters?.isActive !== undefined && { isActive: filters.isActive }),
        ...(filters?.search && {
          OR: [
            {
              name: { contains: filters.search, mode: 'insensitive' as const },
            },
            {
              description: {
                contains: filters.search,
                mode: 'insensitive' as const,
              },
            },
            {
              category: {
                contains: filters.search,
                mode: 'insensitive' as const,
              },
            },
          ],
        }),
      };

      const templates = await prisma.documentTemplate.findMany({
        where,
        include: {
          placeholders: true,
          versions: {
            orderBy: { createdDate: 'desc' },
            take: 1,
          },
          _count: {
            select: {
              generatedDocs: true,
            },
          },
        },
        orderBy: {
          updatedDate: 'desc',
        },
      });

      const categories = await prisma.documentTemplate.groupBy({
        by: ['category'],
        _count: {
          category: true,
        },
        where: { isActive: true },
      });

      const stats = {
        totalTemplates: await prisma.documentTemplate.count(),
        activeTemplates: await prisma.documentTemplate.count({
          where: { isActive: true },
        }),
        totalUsage: await prisma.generatedDocument.count(),
        categoriesCount: categories.length,
        recentlyUsed: [],
      };

      return {
        success: true,
        data: {
          templates,
          total: templates.length,
          categories: categories.map((c) => ({
            name: c.category,
            count: c._count.category,
          })),
          stats,
        } as TemplateSearchResult,
        message: 'Plantillas obtenidas exitosamente',
      };
    } catch (error) {
      console.error('Error fetching templates:', error);
      return {
        success: false,
        message: 'Error al obtener las plantillas',
        errors: { general: ['Error interno del servidor'] },
      };
    }
  });

export const generateDocument = createServerAction()
  .input(generateDocumentSchema)
  .handler(async ({ input }) => {
    try {
      const { templateId, caseId, name, format, data } = input;

      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId },
        include: { placeholders: true },
      });

      if (!template) {
        return {
          success: false,
          message: 'Plantilla no encontrada',
          errors: { templateId: ['Plantilla inválida'] },
        };
      }

      const formattedData = DocumentProcessor.formatDataForTemplate(data);
      const outputFileName = `${name}_${Date.now()}.${format}`;

      const generatedFilePath = await DocumentProcessor.generateDocument(
        template.filePath,
        formattedData,
        outputFileName,
      );

      const generatedDoc = await prisma.generatedDocument.create({
        data: {
          templateId,
          caseId,
          name,
          filePath: generatedFilePath,
          format,
          createdBy: 'current-user-id', // TODO: Get from session
        },
      });

      await prisma.documentTemplate.update({
        where: { id: templateId },
        data: {
          usage: {
            increment: 1,
          },
        },
      });

      revalidatePath('/documents');
      revalidatePath('/documents/templates');

      return {
        success: true,
        data: generatedDoc,
        message: 'Documento generado exitosamente',
      };
    } catch (error) {
      console.error('Error generating document:', error);
      return {
        success: false,
        message: 'Error al generar el documento',
        errors: { general: ['Error interno del servidor'] },
      };
    }
  });

export const updateTemplate = createServerAction()
  .input(updateTemplateSchema)
  .handler(async ({ input }) => {
    try {
      const { id, placeholders, ...updateData } = input;

      const template = await prisma.documentTemplate.update({
        where: { id },
        data: {
          ...updateData,
          ...(placeholders && {
            placeholders: {
              deleteMany: {},
              create: placeholders.map((p) => ({
                placeholder: p.placeholder,
                fieldName: p.fieldName,
                description: p.description,
                dataType: p.dataType,
                isRequired: p.isRequired,
                defaultValue: p.defaultValue,
                validationRule: p.validationRule,
              })),
            },
          }),
        },
        include: {
          placeholders: true,
          versions: true,
        },
      });

      revalidatePath('/documents/templates');

      return {
        success: true,
        data: template,
        message: 'Plantilla actualizada exitosamente',
      };
    } catch (error) {
      console.error('Error updating template:', error);
      return {
        success: false,
        message: 'Error al actualizar la plantilla',
        errors: { general: ['Error interno del servidor'] },
      };
    }
  });

export const deleteTemplate = createServerAction()
  .input(deleteTemplateSchema)
  .handler(async ({ input }) => {
    try {
      const { id } = input;

      const template = await prisma.documentTemplate.findUnique({
        where: { id },
        include: { versions: true },
      });

      if (!template) {
        return {
          success: false,
          message: 'Plantilla no encontrada',
          errors: { id: ['Plantilla inválida'] },
        };
      }

      await FileStorage.deleteFile(template.filePath);

      for (const version of template.versions) {
        await FileStorage.deleteFile(version.filePath);
      }

      await prisma.documentTemplate.delete({
        where: { id },
      });

      revalidatePath('/documents/templates');

      return {
        success: true,
        message: 'Plantilla eliminada exitosamente',
      };
    } catch (error) {
      console.error('Error deleting template:', error);
      return {
        success: false,
        message: 'Error al eliminar la plantilla',
        errors: { general: ['Error interno del servidor'] },
      };
    }
  });
