import { z } from 'zod';
import {
  templatePlaceholderSchema,
  documentTemplateSchema,
  uploadTemplateSchema,
  generateDocumentSchema,
  templateFilterSchema,
  updateTemplateSchema,
  deleteTemplateSchema,
  templateVersionSchema,
  generatedDocumentSchema,
  placeholderDataSchema,
  templateStatsSchema,
  bulkUploadSchema,
} from './schemas';

export type TemplatePlaceholder = z.infer<typeof templatePlaceholderSchema>;
export type DocumentTemplate = z.infer<typeof documentTemplateSchema>;
export type UploadTemplate = z.infer<typeof uploadTemplateSchema>;
export type GenerateDocument = z.infer<typeof generateDocumentSchema>;
export type TemplateFilter = z.infer<typeof templateFilterSchema>;
export type UpdateTemplate = z.infer<typeof updateTemplateSchema>;
export type DeleteTemplate = z.infer<typeof deleteTemplateSchema>;
export type TemplateVersion = z.infer<typeof templateVersionSchema>;
export type GeneratedDocument = z.infer<typeof generatedDocumentSchema>;
export type PlaceholderData = z.infer<typeof placeholderDataSchema>;
export type TemplateStats = z.infer<typeof templateStatsSchema>;
export type BulkUpload = z.infer<typeof bulkUploadSchema>;

export interface DocumentTemplateWithRelations extends DocumentTemplate {
  placeholders: TemplatePlaceholder[];
  versions: TemplateVersion[];
  generatedDocs?: GeneratedDocument[];
  _count?: {
    generatedDocs: number;
  };
}

export interface TemplateCategory {
  name: string;
  count: number;
  subcategories?: TemplateSubcategory[];
}

export interface TemplateSubcategory {
  name: string;
  count: number;
}

export interface TemplateUsageStats {
  templateId: string;
  templateName: string;
  usage: number;
  lastUsed: Date;
  category: string;
}

export interface DocumentGenerationResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  error?: string;
}

export interface PlaceholderValidation {
  fieldName: string;
  isValid: boolean;
  error?: string;
}

export interface TemplatePreview {
  content: string;
  placeholders: TemplatePlaceholder[];
  estimatedSize: number;
}

export interface FileUploadResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  error?: string;
}

export interface BulkUploadResult {
  success: boolean;
  processed: number;
  failed: number;
  results: Array<{
    fileName: string;
    success: boolean;
    templateId?: string;
    error?: string;
  }>;
}

export interface TemplateSearchResult {
  templates: DocumentTemplateWithRelations[];
  total: number;
  categories: TemplateCategory[];
  stats: TemplateStats;
}

export interface DocumentGenerationOptions {
  templateId: string;
  caseId?: string;
  outputFormat: 'docx' | 'pdf';
  fileName?: string;
  data: Record<string, any>;
  includeMetadata?: boolean;
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  placeholders: TemplatePlaceholder[];
}

export interface DocumentDownloadInfo {
  fileName: string;
  mimeType: string;
  size: number;
  buffer: Buffer;
}

export type TemplateStatus = 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
export type DocumentFormat = 'docx' | 'pdf';
export type PlaceholderDataType = 'text' | 'number' | 'date' | 'boolean';
export type GeneratedDocumentStatus = 'GENERATED' | 'DOWNLOADED' | 'SENT';

export interface ActionResult<T> {
  success: boolean;
  data?: T;
  message: string;
  errors?: Record<string, string[]>;
}
